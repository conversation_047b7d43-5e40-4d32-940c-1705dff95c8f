<template>
  <el-dialog
    v-model="dialogVisible"
    title="重算设置"
    width="400px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      label-position="top"
    >
      <el-form-item label="开始时间" prop="startTime" required>
        <el-date-picker
          v-model="form.startTime"
          type="date"
          placeholder="请选择开始时间"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 100%"
        />
      </el-form-item>
      <el-form-item label="结束时间" prop="endTime">
        <el-date-picker
          v-model="form.endTime"
          type="date"
          placeholder="请选择结束时间"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 100%"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          启动
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import type { FormInstance, FormRules } from 'element-plus'
import moment from 'moment'

interface RecalculateForm {
  startTime: string
  endTime: string
}

interface Props {
  visible: boolean
  rowData?: ProcessTableParam
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  rowData: undefined
})

const emit = defineEmits<{
  (evt: 'update:visible', value: boolean): void
  (evt: 'confirm', data: RecalculateForm & { id: string | number; jobId: string; jobName: string }): void
}>()

const dialogVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const formRef = ref<FormInstance>()
const loading = ref(false)

// 表单数据
const form = ref<RecalculateForm>({
  startTime: '',
  endTime: ''
})

// 表单验证规则
const rules: FormRules<RecalculateForm> = {
  startTime: [
    { required: true, message: '请选择开始时间', trigger: 'change' }
  ],
  endTime: [
    { required: true, message: '请选择结束时间', trigger: 'change' }
  ]
}

// 初始化表单数据
const initForm = () => {
  const now = moment()
  const today = now.format('YYYY-MM-DD')

  form.value = {
    startTime: '',
    endTime: today
  }
}

// 监听弹窗显示状态，初始化表单
watch(() => props.visible, (visible) => {
  if (visible) {
    initForm()
  }
})

// 关闭弹窗
const handleClose = () => {
  formRef.value?.resetFields()
  emit('update:visible', false)
}

// 确认提交
const handleConfirm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    if (!props.rowData) {
      ElMessage.error('缺少流程数据')
      return
    }
    
    loading.value = true
    
    // 验证时间逻辑
    if (form.value.startTime && form.value.endTime) {
      const startTime = moment(form.value.startTime, 'YYYY-MM-DD')
      const endTime = moment(form.value.endTime, 'YYYY-MM-DD')
      const today = moment().startOf('day')

      if (startTime.isAfter(endTime)) {
        ElMessage.error('开始时间不能晚于结束时间')
        loading.value = false
        return
      }
      if (endTime.isAfter(today)) {
        ElMessage.error('结束时间不能晚于当前日期')
        loading.value = false
        return
      }
    }
    
    // 提交数据
    emit('confirm', {
      ...form.value,
      id: props.rowData.id,
      jobId: props.rowData.jobId || '',
      jobName: props.rowData.jobName
    })
    
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}
</script>

<style scoped lang="scss">
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
