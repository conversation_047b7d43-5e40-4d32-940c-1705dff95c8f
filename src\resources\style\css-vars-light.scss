html.light {
  --el-color-primary: #165dff;
  --el-color-primary-light-3: #4080ff;
  --el-color-primary-light-5: #6aa1ff;
  --el-color-primary-light-7: #94bfff;
  --el-color-primary-light-8: #bedaff;
  --el-color-primary-light-9: #e8f3fe;
  --el-color-primary-dark-2: #0e42d2;
  --el-color-success: #00b42a;
  --el-color-success-light-3: #23c343;
  --el-color-success-light-5: #4cd263;
  --el-color-success-light-7: #7be188;
  --el-color-success-light-8: #AFFOB5;
  --el-color-success-light-9: #e8ffea;
  --el-color-success-dark-2: #009a29;
  --el-color-warning: #ff7d00;
  --el-color-warning-light-3: #ff9a2e;
  --el-color-warning-light-5: #faac7b;
  --el-color-warning-light-7: #ffcf8b;
  --el-color-warning-light-8: #ffe4ba;
  --el-color-warning-light-9: #fff7e8;
  --el-color-warning-dark-2: #d25f00;
  --el-color-danger: #f53f3f;
  --el-color-danger-light-3: #f76560;
  --el-color-danger-light-5: #f98981;
  --el-color-danger-light-7: #fbaca3;
  --el-color-danger-light-8: #fdcdc5;
  --el-color-danger-light-9: #ffece8;
  --el-color-danger-dark-2: #cb2634;
  --el-color-error: #f53f3f;
  --el-color-error-light-3: #f76560;
  --el-color-error-light-5: #f98981;
  --el-color-error-light-7: #fbaca3;
  --el-color-error-light-8: #fdcdc5;
  --el-color-error-light-9: #ffece8;
  --el-color-error-dark-2: #cb2634;
  --el-color-info: #909399;
  --el-color-info-light-3: #a2a6ae;
  --el-color-info-light-5: #b3b6ba;
  --el-color-info-light-7: #c2c3c7;
  --el-color-info-light-8: #cacbcf;
  --el-color-info-light-9: #f0f2f5;
  --el-color-info-dark-2: #797d86;
  --el-box-shadow: 0px 12px 32px 4px rgba(0, 0, 0, 0.04),
    0px 8px 20px rgba(0, 0, 0, 0.08);
  --el-box-shadow-light: 0px 0px 12px rgba(0, 0, 0, 0.12);
  --el-box-shadow-lighter: 0px 0px 6px rgba(0, 0, 0, 0.12);
  --el-box-shadow-dark: 0px 16px 48px 16px rgba(0, 0, 0, 0.08),
    0px 12px 32px rgba(0, 0, 0, 0.12), 0px 8px 16px -8px rgba(0, 0, 0, 0.16);
  --el-bg-color-page: #f2f3f5;
  --el-bg-color: #ffffff;
  --el-bg-color-overlay: #ffffff;
  --el-text-color-primary: #1d2129;
  --el-text-color-regular: #4e5969;
  --el-text-color-secondary: #86909c;
  --el-text-color-placeholder: #a4adb8;
  --el-text-color-disabled: #c9cdd4;
  --el-border-color-darker: #b5bfcb;
  --el-border-color-dark: #c9cdd4;
  --el-border-color: #e5e6eb;
  --el-border-color-light: #eaeaef;
  --el-border-color-lighter: #f0f0f2;
  --el-border-color-extra-light: #f5f5f5;
  --el-fill-color-darker: #e6e8eb;
  --el-fill-color-dark: #ebedf0;
  --el-fill-color: #f0f2f5;
  --el-fill-color-light: #f5f7fa;
  --el-fill-color-lighter: #fafafa;
  --el-fill-color-extra-light: #fafcff;
  --el-fill-color-blank: transparent;
  --el-mask-color: rgba(255, 255, 255, 0.9);
  --el-mask-color-extra-light: rgba(255, 255, 255, 0.3);
  --el-status-bg-color-0: rgba(85, 205, 135, 0.1);
  --el-status-text-color-0: rgba(85, 205, 135, 1);
  --el-status-bg-color-1: rgba(76, 166, 255, 0.1);
  --el-status-text-color-1: rgba(76, 166, 255, 1);
  --el-status-bg-color-2: rgba(255, 209, 47, 0.1);
  --el-status-text-color-2: rgba(255, 209, 47, 1);
  --el-status-bg-color-3: rgba(241, 132, 196, 0.1);
  --el-status-text-color-3: rgba(241, 132, 196, 1);
  --el-status-bg-color-4: rgba(141, 123, 254, 0.1);
  --el-status-text-color-4: rgba(141, 123, 254, 1);
  --el-status-bg-color-5: rgba(125, 217, 255, 0.1);
  --el-status-text-color-5: rgba(125, 217, 255, 1);
}

html.light .el-table {
  --el-table-header-bg-color: #f8fafb;
}

// html.light .el-button {
//   --el-button-border-color: var(--el-border-color-dark);
//   --el-button-disabled-bg-color: var(--el-fill-color-light);
//   --el-button-disabled-text-color: var(--el-text-color-disabled);
//   --el-button-disabled-border-color: var(--el-border-color-dark);
// }

html.light .el-card {
  --el-card-bg-color: var(--el-bg-color-overlay);
}

html.light .el-switch {
  --el-switch-on-color: var(--el-color-success);
}

html.light .el-empty {
  --el-empty-fill-color-0: var(--el-color-white);
  --el-empty-fill-color-1: #fcfcfd;
  --el-empty-fill-color-2: #f8f9fb;
  --el-empty-fill-color-3: #f7f8fc;
  --el-empty-fill-color-4: #eeeff3;
  --el-empty-fill-color-5: #edeef2;
  --el-empty-fill-color-6: #e9ebef;
  --el-empty-fill-color-7: #e5e7e9;
  --el-empty-fill-color-8: #e0e3e9;
  --el-empty-fill-color-9: #d5d7de;
}

// 引入TDesign
html.light .t-steps {
  --td-brand-color: var(--el-color-primary);
}

// 流程设计连接线颜色
html.light .custom-g-edge {
  --flow-edge-default-color: #282c34;
  --flow-edge-hover-color: #4075e5;
}
