<template>
  <div class="page">
    <el-container class="container">
      <el-header class="header">
        <div class="header-left">
          <el-button type="primary" @click="addList" :icon="CirclePlus">
            新增流程
          </el-button>
          <el-input
            v-model="searchValue"
            placeholder="请输入流程名称关键字"
            class="input-style"
            :suffix-icon="Search"
            @change="searchChange"
          ></el-input>
        </div>
        <div class="header-right">
          <el-checkbox
            style="margin-right: 8px"
            v-model="allSelect"
            v-show="!isTableLayout"
            @change="changeAllSelect"
            :indeterminate="isIndeterminate"
            :disabled="isEmpty"
          >
            全选
          </el-checkbox>
          <el-button @click="exportList" :disabled="isDisabled">
            批量导出
          </el-button>
          <el-upload
            class="upload-style"
            ref="uploadRef"
            :auto-upload="false"
            :multiple="false"
            :show-file-list="false"
            :on-change="handleChange"
          >
            <el-button>导入流程</el-button>
          </el-upload>
          <el-button type="primary" @click="refreshList" :icon="RefreshRight">
            刷新
          </el-button>
          <el-image
            :src="isTableLayout ? tableUrl[0] : tableUrl[1]"
            class="image-style"
            @click="isTableLayout = true"
          ></el-image>
          <el-image
            :src="isTableLayout ? cardUrl[1] : cardUrl[0]"
            class="image-style"
            @click="isTableLayout = false"
          ></el-image>
        </div>
      </el-header>
      <el-main class="main-container" v-if="tableData.length > 0">
        <TableLayout
          v-if="isTableLayout"
          :tableData="tableData"
          :selectedValue="selectedValue"
          @routeClick="routeClick"
          @deleteClick="deleteClick"
          @handleClick="handleClick"
          @recalculateClick="recalculateClick"
          @cancelRecalculateTask="cancelRecalculateTask"
          @refreshList="refreshList"
          @handleSelectionChange="handleSelectionChange"
          @runFlinkTask="runFlinkTask"
          @cancelFlinkTask="cancelFlinkTask"
        ></TableLayout>
        <CardLayout
          v-else
          :tableData="tableData"
          :watchAllSelect="watchAllSelect"
          :selectedValue="selectedValue"
          :isIndeterminate="isIndeterminate"
          @routeClick="routeClick"
          @deleteClick="deleteClick"
          @handleClick="handleClick"
          @recalculateClick="recalculateClick"
          @cancelRecalculateTask="cancelRecalculateTask"
          @refreshList="refreshList"
          @handleSelectionChange="handleSelectionChange"
          @runFlinkTask="runFlinkTask"
          @cancelFlinkTask="cancelFlinkTask"
        ></CardLayout>
        <el-pagination
          class="footer_style"
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :small="false"
          :disabled="false"
          :background="true"
          layout="total,sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </el-main>
      <el-empty
        v-else
        :image="emptyUrl"
        :image-size="400"
        style="margin-top: 10%"
      />
    </el-container>
    <AddProcess
      v-model="isShowDialog"
      :inputData="inputData"
      @refreshList="refreshList"
    ></AddProcess>
    <RecalculateDialog
      v-model:visible="isShowRecalculateDialog"
      :rowData="currentRecalculateRow"
      @confirm="handleRecalculateConfirm"
    ></RecalculateDialog>
  </div>
</template>

<script setup lang="ts">
import CardLayout from "./components/cardLayout.vue";
import TableLayout from "./components/tableLayout.vue";
import RecalculateDialog from "./components/recalculateDialog.vue";

import { Search, CirclePlus, RefreshRight } from "@element-plus/icons-vue";
// 接口导入
import customApi from "@/api/custom";
import { recalculateFlinkJob } from "@/api/processListApi";

import { useRouter, useRoute } from "vue-router";
const router = useRouter();

import { useflowChartStore } from "@/store/modules/flowChart";
const flowChartStore = useflowChartStore();

function routeClick(row: ProcessTableParam, type: string) {
  flowChartStore.clearOperatorData();
  router.push({
    path: "/flowChart",
    state: {
      id: row.id,
      jobName: row.jobName,
      alias: row.alias,
      status: type,
      isTableLayout: isTableLayout.value.toString(),
    },
  });
}

const routeState = window.history.state;

// 判断初始进入流程设计页面前是表格还是卡片
watch(
  () => routeState.isTableLayout,
  (newVal) => {
    // 判断是否是页面刷新还是页面跳转并去除第一次打开页面的监听
    if (!routeState.scroll && typeof newVal === "boolean") {
      nextTick(() => {
        isTableLayout.value = newVal;
      });
    }
  },
  { immediate: true, deep: true }
);

// 引入新增/编辑弹窗
import AddProcess from "./components/addProcess.vue";

const tableData = ref<ProcessTableParam[]>([]);

// 判断运行按钮是否禁用
const isDisabled = computed(() => {
  return selectedValue.value.length === 0;
});

const searchValue = ref("");

function searchChange() {
  // 按照后端要求进行搜索时返回第一页进行全局搜索
  currentPage.value = 1;
  pageParams.index = 0;
  queryProcessList();
}

// 表格勾选框选中数据
const selectedValue = ref<ProcessTableParam[]>([]);

// 重算弹窗相关状态
const isShowRecalculateDialog = ref(false);
const currentRecalculateRow = ref<ProcessTableParam>();

const handleSelectionChange = (val: ProcessTableParam[]) => {
  selectedValue.value = val;
  allSelect.value = selectedValue.value.length === tableData.value.length;
  if (allSelect.value) {
    watchAllSelect.value = true;
  }
  isIndeterminate.value = !(!val.length || allSelect.value);
};

async function runFlinkTask(task: ProcessTableParam) {
  const param = {
    id: task.id,
    jobName: task.jobName,
    argues: task.argues,
  };
  try {
    const res = await customApi.runFlinkJob(param);
    ElMessage.success(res.msg);
    queryProcessList();
  } catch (error) {
    // 接口失败要重新置回原来的值
    task.isRunning = !task.isRunning;
    console.log(error);
  }
}

async function cancelFlinkTask(task: ProcessTableParam) {
  if (!task.jobId) return ElMessage.error("该流程jobId不存在");
  const param = {
    id: task.id,
    jobName: task.jobName,
    jobId: task.jobId,
  };
  try {
    const res = await customApi.cancelFlinkJob(param);
    ElMessage.success(res.msg);
    queryProcessList();
  } catch (error) {
    // 接口失败要重新置回原来的值
    task.isRunning = !task.isRunning;
    console.log(error);
  }
}

import { getExportJSON } from "@/utils/common";

// 导出流程列表
async function exportList() {
  if (selectedValue.value.length === 0) {
    return ElMessage.error("请选择要导出的流程");
  }
  const ids = selectedValue.value.map((item) => item.id);
  try {
    const res = await customApi.exportRulesFile(ids);
    getExportJSON(res);
  } catch (error) {
    console.log(error);
  }
}

import type { UploadProps, UploadInstance } from "element-plus";

const uploadRef = ref<UploadInstance>();

// 选择文件后获取文件信息
const handleChange: UploadProps["onChange"] = async (uploadFile) => {
  // 先判断是否时json文件
  if (!uploadFile.name.includes(".json")) {
    return ElMessage.error("请选择json文件");
  }
  let params = new FormData();
  if (!uploadFile.raw) return;
  params.append("rulesFile", uploadFile.raw);
  try {
    const res = await customApi.importRulesFile(params);
    ElMessage.success(res.msg);
    queryProcessList();
  } catch (error) {}
};

// 刷新按钮
function refreshList() {
  queryProcessList();
}

// 图片切换
const tableUrl = [
  new URL("../../assets/images/table_blue.png", import.meta.url).href,
  new URL("../../assets/images/table_white.png", import.meta.url).href,
];

const cardUrl = [
  new URL("../../assets/images/card_blue.png", import.meta.url).href,
  new URL("../../assets/images/card_white.png", import.meta.url).href,
];

// 当前是表格布局还是卡片布局
const isTableLayout = ref<boolean>(true);

const isShowDialog = ref<boolean>(false);

const inputData = ref<ProcessTableParam>({
  id: "",
  alias: "",
  jobName: "",
});

function addList() {
  isShowDialog.value = true;
  inputData.value = {
    id: "",
    alias: "",
    jobName: "",
  };
}

function handleClick(row: ProcessTableParam) {
  isShowDialog.value = true;
  inputData.value = row;
}

function deleteClick(row: ProcessTableParam) {
  const callback = () => {
    deleteFlinkTask(row.id);
  };
  comfirmTemplate(`确定要删除"${row.alias}"流程吗？`, "取消删除", callback);
}

// 删除flink任务
async function deleteFlinkTask(id: string | number) {
  try {
    const res = await customApi.deleteFlinkJob({ id });
    ElMessage.success(res.msg);
    queryProcessList();
  } catch (error) {
    console.log(error);
  }
}

// 弹出二次确定弹窗模板
function comfirmTemplate(
  titleTip: string,
  cancelTip: string,
  callback?: Function
) {
  ElMessageBox.confirm(titleTip, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      callback && callback();
    })
    .catch(() => {
      ElMessage.warning(cancelTip);
    });
}

// 重算点击事件
function recalculateClick(row: ProcessTableParam) {
  currentRecalculateRow.value = row;
  isShowRecalculateDialog.value = true;
}

// 重算确认事件
async function handleRecalculateConfirm(data: any) {
  try {
    const result = await recalculateFlinkJob(data);
    if (result.code === 200 || result.code === 200) {
      ElMessage.success('重算任务已启动');
      isShowRecalculateDialog.value = false;

      // 立即更新当前行的重算状态，保持流程状态不变
      if (currentRecalculateRow.value) {
        // 只更新重算相关字段，不修改流程状态status
        currentRecalculateRow.value.recalculateStatus = 'RECALCULATING';
        currentRecalculateRow.value.isRecalculating = true;
        // 设置重算运行时间为当前时间
        currentRecalculateRow.value.recalculateRunningTime = new Date().toISOString().replace('T', ' ').substring(0, 19);
      }

      // 刷新列表以获取最新状态
      queryProcessList();
    } else {
      ElMessage.error(result.msg || '重算启动失败');
    }
  } catch (error) {
    console.error('重算失败:', error);
    ElMessage.error('重算启动失败');
  }
}

// 取消重算任务
async function cancelRecalculateTask(row: ProcessTableParam) {
  if (!row.jobId) return ElMessage.error("该流程jobId不存在");
  const param = {
    id: row.id,
    jobName: row.jobName,
    jobId: row.jobId,
  };
  try {
    // 这里应该调用取消重算的API
    const res = await customApi.cancelFlinkJob(param);
    ElMessage.success(res.msg);

    // 只更新重算相关状态，保持流程状态不变
    row.recalculateStatus = 'CANCELED'; // 或其他适当的重算状态
    row.isRecalculating = false;
    row.recalculateRunningTime = undefined;

    // 刷新列表
    queryProcessList();
  } catch (error) {
    console.error('取消重算失败:', error);
    ElMessage.error('取消重算失败');
  }
}

// 勾选框全选
const allSelect = ref(false);

// 勾选框的中间状态
const isIndeterminate = ref(false);

// 用于监听是否是由于点击全选框触发的全选
const watchAllSelect = ref(false);

function changeAllSelect(value: string | number | boolean) {
  watchAllSelect.value = isIndeterminate.value ? false : (value as boolean);
  isIndeterminate.value = false;
}

// 定义分段数据
const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);

// 定义分页入参, 该参数需要放在
const pageParams = reactive({
  index: 0,
  limit: 20,
});

function handleSizeChange(val: number) {
  currentPage.value = 1;
  pageSize.value = val;
  pageParams.index = 0;
  pageParams.limit = val;
  queryProcessList();
}

function handleCurrentChange(val: number) {
  currentPage.value = val;
  pageParams.index = (val - 1) * pageSize.value;
  queryProcessList();
}

// 初始化查询列表信息
async function queryProcessList() {
  const params = {
    ...pageParams,
    jobAlias: searchValue.value,
  };
  try {
    const res = await customApi.queryFlinkJob(params);
    tableData.value = res.data || [];
    addRunningColumn();
    total.value = res.total || 0;
    clearActive();
  } catch (error) {
    tableData.value = [];
    total.value = 0;
    console.log(error);
  }
}

const RUNNING_STATUS = ["RUNNING", "SUBMITTED"];
const RECALCULATING_STATUS = ["RUNNING"];

// 表格数据添加运行状态一列
function addRunningColumn() {
  tableData.value?.forEach((item: ProcessTableParam) => {
    if (item.status) {
      item.isRunning = RUNNING_STATUS.includes(item.status);
    }
    // 重算状态基于recalculateStatus字段判断
    if (item.recalculateStatus) {
      item.isRecalculating = RECALCULATING_STATUS.includes(item.recalculateStatus);
    } else {
      item.isRecalculating = false;
    }
  });
}

// 清空选择状态
function clearActive() {
  watchAllSelect.value = false;
  selectedValue.value.length = 0;
  isIndeterminate.value = false;
}

// 空图片展示
const emptyUrl = new URL("../../assets/images/empty.png", import.meta.url).href;

const isEmpty = computed(() => {
  return tableData.value.length === 0;
});

onMounted(() => {
  // 查询列表信息
  queryProcessList();
});
</script>

<style lang="scss" scoped>
.page {
  width: 100%;
  height: 100%;
  position: relative;
  box-sizing: border-box;
  background: #f0f0f0;
  .container {
    background: #ffffff;
    height: 100%;
    min-width: 920px;
    padding: 24px;
    .header {
      padding: 0px;
      height: 32px;
      display: flex;
      justify-content: space-between;
      .header-right {
        display: flex;
        .upload-style {
          margin: 0px 8px;
        }
        .image-style {
          margin-left: 8px;
          cursor: pointer;
          width: 32px;
          height: 32px;
        }
      }
      .input-style {
        width: 240px;
        margin-left: 8px;
      }
    }
    .main-container {
      flex: 1;
      padding: 0;
      margin-top: 24px;
    }
  }
}
</style>
