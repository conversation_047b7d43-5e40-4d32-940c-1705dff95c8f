<template>
  <svg class="svg-icon" aria-hidden="true" v-bind="$attrs">
    <use :href="iconName" />
  </svg>
</template>

<script setup lang="ts">
const props = defineProps<{
  name: string;
}>();

const iconName = computed(() => {
  return `#icon-${props.name}`;
});
</script>

<style lang="scss" scoped>
.svg-icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
</style>
