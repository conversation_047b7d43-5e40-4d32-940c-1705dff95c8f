<template>
  <div class="page">
    <el-form
      class="form-style form-border"
      ref="ruleFormRef"
      :model="inputOperatorData"
      :rules="inputOperatorDataRules"
      :show-message="false"
      label-width="auto"
      label-position="top"
    >
      <el-form-item prop="sql" style="margin-bottom: 0">
        <VAceEditor
          v-model:value="code"
          lang="sql"
          ref="editorRef"
          style="height: 620px; width: 100%"
          :options="editorOptions"
        />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { VAceEditor } from "vue3-ace-editor";
import "ace-builds/src-noconflict/mode-sql.js";
import "ace-builds/src-noconflict/theme-chrome";

// 启动提示规则
import "ace-builds/src-noconflict/ext-language_tools";
import "ace-builds/src-noconflict/snippets/sql";

import { format } from "sql-formatter";

// 对sql语句进行格式化处理
function formatterSql() {
  code.value = format(code.value);
}

const code = ref("");

const editorRef = ref();

const editorOptions = {
  fontSize: 14,
  tabSize: 2,
  readOnly: true, // 只读
  useWorker: true, // 启用语法检查,必须为true
  enableSnippets: true,
  enableBasicAutocompletion: true, // 自动补全
  enableLiveAutocompletion: true, // 智能补全
  showPrintMargin: false, // 去掉灰色的线
  wrap: false,
};

const getEditorContent = () => {
  if (!editorRef.value) return;
  const editor = editorRef.value?.getAceInstance();
  const sqlCode = editor.getValue();
  inputOperatorData.value.sql = sqlCode.replace(/\n/g, " ");
};

// 获取父组件的ref
const props = defineProps<{
  showDrawer: boolean;
  stepActive: number;
}>();

// 获取爷爷的响应式数据
const inputData = inject("inputData") as Ref<AllInputParam>;

// 数据监听  -- 通过监听去对弹窗数据进行重置,弹窗打开就触发
watch(
  () => props.showDrawer,
  async (val) => {
    if (!val) return;
    // 使用async和await来处理nextTick，减少代码层级
    await nextTick();
    inputOperatorData.value.id = inputData.value.id;
  },
  {
    immediate: true,
  }
);

import { useflowChartStore } from "@/store/modules/flowChart";
const flowChartStore = useflowChartStore();

// 监听步骤发现改变获取最新的数据
watch(
  () => props.stepActive,
  (val) => {
    if (val === 1) {
      inputOperatorData.value.sql =
        flowChartStore.inputSource[inputData.value.id]?.sql || "";
      code.value = inputOperatorData.value.sql;
      formatterSql();
    }
  }
);

const inputOperatorData = ref<AllInputParam>({
  id: "",
  sql: "",
});

import type { FormInstance } from "element-plus";

const ruleFormRef = ref<FormInstance>();

const inputOperatorDataRules = {
  sql: [
    {
      required: false,
      trigger: ["blur", "change"],
    },
  ],
};

// 引入通用的校验规则
import { getFormRules } from "@/utils/common";

async function getNewData() {
  getEditorContent();
  const childRuleBool = await getFormRules(
    ruleFormRef.value,
    inputData.value.nodeType,
    inputOperatorData.value,
    "基本配置"
  );
  return childRuleBool;
}

// 放开方法给父组件使用
defineExpose({
  getNewData,
});
</script>

<style lang="scss" scoped>
.page {
  height: calc(100% - 80px);
}
.form-border {
  border: 1px solid rgb(204, 204, 204);
  padding: 8px;
  border-radius: 2px;
}
</style>
