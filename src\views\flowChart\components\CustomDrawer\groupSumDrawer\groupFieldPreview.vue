<template>
  <div class="page page-table">
    <el-button class="btn-refresh" @click="refeshTable" :icon="RefreshRight">
      刷新
    </el-button>
    <div class="form-style form-table-style">
      <el-form
        class="fullheight"
        ref="ruleFormRef"
        :model="groupSumData"
        :rules="groupSumDataRules"
        :show-message="false"
        label-width="auto"
        v-show="!isEmpty"
      >
        <el-form-item prop="fieldsArray" class="form-table-height">
          <el-table :data="tableData" scrollbar-always-on class="fullheight">
            <template v-for="item in fieldTableHeader">
              <el-table-column
                v-bind="item"
                show-overflow-tooltip
              ></el-table-column>
            </template>
          </el-table>
        </el-form-item>
      </el-form>
      <el-empty v-show="isEmpty" :image="emptyUrl" :image-size="400" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { RefreshRight } from "@element-plus/icons-vue";

// 引入通用的校验规则
import { getFormRules, nodeTypeFieldMap } from "@/utils/common";

// 获取父组件的ref
const props = defineProps<{
  showDrawer: boolean;
  keyMenuItem: string;
}>();

// 获取爷爷的响应式数据
const inputData = inject("inputData") as Ref<EditDrawerParam>;

// 数据监听  -- 通过监听去对弹窗数据进行重置
watch(
  () => props.showDrawer,
  async (val) => {
    if (!val) return;
    // 使用async和await来处理nextTick，减少代码层级
    await nextTick();
    groupSumData.value.id = inputData.value.id;
  },
  {
    immediate: true,
  }
);

const groupSumData = ref<EditDrawerParam>({
  id: "",
});

import { fieldTableHeader } from "@/utils/globalField";

const tableData = ref<FieldTableParam[]>([]);

// 定义页面名称
const componentPageName = "groupFieldPreview";

watch(
  () => props.keyMenuItem,
  (newVal, oldVal) => {
    if (newVal === componentPageName) {
      queryFieldTableData();
    }
  }
);

import { useflowChartStore } from "@/store/modules/flowChart";
const flowChartStore = useflowChartStore();

function refeshTable() {
  queryFieldTableData();
}

// 获取表格字段数据
async function queryFieldTableData() {
  // 清空数据
  tableData.value = [];
  const type = inputData.value.type;
  if (!type) return;
  const data = await nodeTypeFieldMap(type, flowChartStore.tempComputerData);
  tableData.value = cloneDeep(data);
}

import type { FormInstance } from "element-plus";

const ruleFormRef = ref<FormInstance>();

const groupSumDataRules = {
  fieldsArray: [
    {
      required: false,
      trigger: ["blur", "change"],
    },
  ],
};

async function getNewData() {
  const childRuleBool = await getFormRules(
    ruleFormRef.value,
    inputData.value.nodeType,
    groupSumData.value,
    "数据预览"
  );
  return childRuleBool;
}

// 空图片展示
const emptyUrl = new URL("@/assets/images/empty.png", import.meta.url).href;

const isEmpty = computed(() => {
  return tableData.value.length === 0;
});

// 放开方法给父组件使用
defineExpose({
  getNewData,
});
</script>

<style lang="scss" scoped>
.form-table-style {
  max-height: calc(100% - 50px);
}
</style>
