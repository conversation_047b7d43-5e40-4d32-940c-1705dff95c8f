<template>
  <div class="page">
    <el-form
      class="form-style"
      ref="ruleFormRef"
      label-position="top"
      require-asterisk-position="right"
      label-width="auto"
      :model="exceptionHandlerData"
      :rules="exceptionHandlerDataRules"
      :show-message="false"
    >
      <el-form-item label="节点名称" prop="name">
        <el-input v-model="exceptionHandlerData.name" maxlength="20"></el-input>
        <div class="form-item-tips">指定任何有意义且能描述本节点的说明</div>
      </el-form-item>
      <el-form-item label="节点ID" prop="id">
        <el-input v-model="exceptionHandlerData.id" disabled></el-input>
        <div class="form-item-tips">节点ID不能重复</div>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="exceptionHandlerData.remark"
          type="textarea"
          :rows="1"
        ></el-input>
      </el-form-item>
      <el-form-item label="业务场景" prop="businessScene">
        <el-select
          v-model="exceptionHandlerData.businessScene"
          filterable
          @change="changeScene"
        >
          <el-option
            v-for="item in businessSceneOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <div class="form-item-tips">请选择一个业务场景</div>
      </el-form-item>
      <template v-for="fieldItem in dynamicFieldOptions" :key="fieldItem.field">
        <el-form-item
          :label="fieldItem.fieldName"
          :prop="'dynamicField.' + fieldItem.field"
          v-if="exceptionHandlerData.dynamicField"
        >
          <div style="display: flex; width: 100%">
            <el-select
              v-model="exceptionHandlerData.dynamicField[fieldItem.field]"
              filterable
              :multiple="fieldItem.isChooseMore"
              :collapse-tags="fieldItem.isChooseMore"
              collapse-tags-tooltip
              :max-collapse-tags="2"
            >
              <el-option
                v-for="item in upstreamFieldOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-button
              style="margin-left: 4px"
              :icon="RefreshRight"
              @click="loadField"
            >
              载入字段
            </el-button>
          </div>
          <div class="form-item-tips">
            {{ fieldItem.description }}
          </div>
        </el-form-item>
      </template>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { RefreshRight } from "@element-plus/icons-vue";

// 引入通用的校验规则
import {
  getFormRules,
  getConnectData,
  getNodeFrom,
  getResetField,
  getCommonEnumField,
} from "@/utils/common";

import { useflowChartStore } from "@/store/modules/flowChart";
const flowChartStore = useflowChartStore();

const props = defineProps<{
  showDrawer: boolean;
  keyMenuItem: string;
  componentPageName: string;
}>();

// 获取爷爷的响应式数据
const inputData = inject("inputData") as Ref<EditDrawerParam>;

// 数据监听  -- 通过监听去对弹窗数据进行重置
watch(
  () => props.showDrawer,
  async (val) => {
    if (!val) return;
    // 使用async和await来处理nextTick，减少代码层级
    await nextTick();
    exceptionHandlerData.value = cloneDeep(inputData.value);
    // 初始化
    initData();
    refreshOptions();
    // 延迟进行表单校验的清空操作才会生效,处理下拉框的change导致进入弹窗的校验触发
    setTimeout(() => {
      clearValidate();
    }, 10);
  },
  {
    immediate: true,
  }
);

// 初始化数据
function initData() {
  exceptionHandlerData.value.from ??= [];
  exceptionHandlerData.value.to = exceptionHandlerData.value.id;
  exceptionHandlerData.value.dynamicField ??= {};
  // 初始化的时候就先清空原先的下拉框options数据
  dynamicFieldOptions.value.length = 0;
  upstreamFieldOptions.value.length = 0;
}

/**
 * @description: 回显判断是否重新获取字段信息
 * @return {*}
 */
async function refreshOptions() {
  getlocatedData();
  queryBusinessData();

  const { businessScene, dynamicField } = exceptionHandlerData.value;

  // 判断业务场景是否存在
  if (businessScene) {
    changeScene(businessScene);
  }

  // 判断是否已经存在上游字段
  if (!isEmpty(dynamicField)) {
    await loadField();
  }
}

// 用于监听菜单是否进行切换到指定页面
watch(
  () => props.keyMenuItem,
  (newVal, oldVal) => {
    if (oldVal === props.componentPageName) {
      flowChartStore.updateTempData(
        inputData.value.nodeType,
        exceptionHandlerData.value
      );
    }
  }
);

const exceptionHandlerData = ref<EditDrawerParam>({
  id: "",
  name: "",
});

import type { FormInstance, FormRules } from "element-plus";

const ruleFormRef = ref<FormInstance>();

const exceptionHandlerDataRules = reactive<FormRules<EditDrawerParam>>({
  id: [
    {
      required: true,
      trigger: ["blur", "change"],
    },
  ],
  businessScene: [
    {
      required: true,
      trigger: ["blur", "change"],
    },
  ],
});

import { useVueFlow } from "@vue-flow/core";

const { getIncomers } = useVueFlow();

// 数据所在节点
const locatedNodeOptions = ref<ComputerOptionsParam[]>([]);

// 上游字段集合
const upstreamFieldOptions = ref<NodeOptionsParam[]>([]);

/**
 * @description: 获取与之连接的上游算子数据
 * @return {*}
 */
function getlocatedData() {
  locatedNodeOptions.value.length = 0;
  // 获取当前节点的所有入边
  const incomers = getIncomers(inputData.value.id);
  locatedNodeOptions.value = getResetField(incomers);
}

async function loadField() {
  upstreamFieldOptions.value.length = 0;
  // 调用方法获取字段数据
  const resData = (await getConnectData(
    locatedNodeOptions.value
  )) as ConnectDataParam;
  const { getTableData, filterData } = resData;
  exceptionHandlerData.value.dataStream = filterData?.dataStream;
  if (isEmpty(getTableData)) return;
  exceptionHandlerData.value.upstreamFieldTable = getTableData;
  upstreamFieldOptions.value = getCommonEnumField(getTableData);
}

//  业务场景具体内容
const businessSceneOptions = ref<OptionsParam[]>([]);

// 接口导入
import customApi from "@/api/custom";

/**
 * @description: 查询业务场景数据
 * @return {*}
 */
async function queryBusinessData() {
  try {
    const res = await customApi.getBusinessScenarios();
    businessSceneOptions.value = cloneDeep(res.data);
  } catch (error) {
    console.log(error);
  }
}

/**
 * @description: 业务场景切换
 * @param {*} val - 当前业务场景
 * @return {*}
 */
function changeScene(val: string) {
  queryDynamicField(val);
}

// 业务场景关联字段集合
const dynamicFieldOptions = ref<DynamicOptionsParam[]>([]);

/**
 * @description: 业务场景关联字段集合
 * @param {*} val - 当前业务场景
 * @return {*}
 */
async function queryDynamicField(val: string | number) {
  try {
    const res = await customApi.getRequiredParameters({ id: val });
    dynamicFieldOptions.value = cloneDeep(res.data);
  } catch (error) {
    console.log(error);
  }
}

// 监听业务场景关联字段集合，来获取关联字段的校验规则
watch(
  () => dynamicFieldOptions.value,
  (val) => {
    if (!val || val.length === 0) return;
    val.forEach((item) => {
      exceptionHandlerDataRules[`dynamicField.${item.field}`] = [
        {
          required: item.isRequired,
          trigger: ["blur", "change"],
        },
      ];
    });
  },
  {
    deep: true,
  }
);

async function getNewData() {
  exceptionHandlerData.value.from = getNodeFrom(locatedNodeOptions.value);
  // 判断是否校验通过
  const childRuleBool = await getFormRules(
    ruleFormRef.value,
    inputData.value.nodeType,
    exceptionHandlerData.value,
    "基本配置"
  );
  return childRuleBool;
}

// 判断输入源是否修改导致字段进行修改
function checkFieldUpdate() {
  if (!inputData.value.businessScene) return false;
  if (
    exceptionHandlerData.value.businessScene !== inputData.value.businessScene
  ) {
    return true;
  }

  if (isEmpty(inputData.value.dynamicField)) return false;
  return !isEqual(
    exceptionHandlerData.value.dynamicField,
    inputData.value.dynamicField
  );
}

// 关闭弹窗清楚校验
function clearValidate() {
  ruleFormRef.value?.clearValidate();
}

// 放开方法给父组件使用
defineExpose({
  getNewData,
  checkFieldUpdate,
});
</script>

<style lang="scss" scoped>
.page {
  height: calc(100% - 60px);
  padding-right: 8px;
  overflow-y: auto;
  overflow-x: hidden;
}
</style>
