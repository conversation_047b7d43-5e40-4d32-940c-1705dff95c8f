import { describe, it, expect } from 'vitest'

describe('重算状态修复验证', () => {
  // 模拟ProcessTableParam类型
  interface ProcessTableParam {
    id: string | number
    status?: string // 流程状态
    recalculateStatus?: string // 重算状态
    isRunning?: boolean
    isRecalculating?: boolean
    recalculateRunningTime?: string
    updateTime?: string
  }

  // 模拟重算状态判断函数
  function isRecalculatingSubmittingStatus(row: ProcessTableParam): boolean {
    // 重算状态为RECALCULATING时，应该显示"运行"状态，不是提交状态
    return false;
  }

  // 模拟重算运行时间获取函数
  function getRecalculateRunning(row: ProcessTableParam): string {
    const RECALCULATE_RUNNING_STATUS = ["RECALCULATING"];

    if (row.recalculateStatus && RECALCULATE_RUNNING_STATUS.includes(row.recalculateStatus)) {
      const timeValue = row.recalculateRunningTime || row.updateTime;
      return timeValue || "--";
    }

    return "--";
  }

  // 模拟运行状态设置函数
  function addRunningColumn(tableData: ProcessTableParam[]): void {
    const RUNNING_STATUS = ["RUNNING", "SUBMITTED"];
    const RECALCULATING_STATUS = ["RECALCULATING"];
    
    tableData.forEach((item: ProcessTableParam) => {
      if (item.status) {
        item.isRunning = RUNNING_STATUS.includes(item.status);
      }
      // 重算状态基于recalculateStatus字段判断
      if (item.recalculateStatus) {
        item.isRecalculating = RECALCULATING_STATUS.includes(item.recalculateStatus);
      }
    });
  }

  // 模拟重算确认后的状态更新
  function handleRecalculateConfirm(row: ProcessTableParam): void {
    // 只更新重算相关字段，不修改流程状态status
    row.recalculateStatus = 'RECALCULATING';
    row.isRecalculating = true;
    row.recalculateRunningTime = new Date().toISOString().replace('T', ' ').substring(0, 19);
  }

  // 模拟取消重算后的状态更新
  function cancelRecalculateTask(row: ProcessTableParam): void {
    // 只更新重算相关状态，保持流程状态不变
    row.recalculateStatus = 'STOPPED';
    row.isRecalculating = false;
    row.recalculateRunningTime = undefined;
  }

  // 模拟重算状态显示文本函数
  function getRecalculateStatusText(recalculateStatus: string): string {
    if (!recalculateStatus) return "--";

    // 重算状态为RECALCULATING时显示"running"
    if (recalculateStatus === "RECALCULATING") {
      return "running";
    }

    return recalculateStatus;
  }

  it('重算状态应该显示"运行"而不是"提交"', () => {
    const testCases = [
      { recalculateStatus: 'RECALCULATING', expected: false },
      { recalculateStatus: 'STOPPED', expected: false },
      { recalculateStatus: undefined, expected: false },
    ];

    testCases.forEach(({ recalculateStatus, expected }) => {
      const row: ProcessTableParam = { id: 1, recalculateStatus };
      expect(isRecalculatingSubmittingStatus(row)).toBe(expected);
    });
  });

  it('重算状态为RECALCULATING时应该显示"running"', () => {
    const testCases = [
      { recalculateStatus: 'RECALCULATING', expected: 'running' },
      { recalculateStatus: 'STOPPED', expected: 'STOPPED' },
      { recalculateStatus: 'FAILED', expected: 'FAILED' },
      { recalculateStatus: '', expected: '--' },
      { recalculateStatus: undefined, expected: '--' },
    ];

    testCases.forEach(({ recalculateStatus, expected }) => {
      expect(getRecalculateStatusText(recalculateStatus || '')).toBe(expected);
    });
  });

  it('重算运行时间应该基于recalculateStatus字段', () => {
    const testCases = [
      {
        row: { id: 1, recalculateStatus: 'RECALCULATING', recalculateRunningTime: '2024-01-01 10:00:00' },
        expected: '2024-01-01 10:00:00'
      },
      {
        row: { id: 2, recalculateStatus: 'RECALCULATING', updateTime: '2024-01-01 11:00:00' },
        expected: '2024-01-01 11:00:00'
      },
      {
        row: { id: 3, recalculateStatus: 'STOPPED' },
        expected: '--'
      },
      {
        row: { id: 4, status: 'RECALCULATING' }, // 注意：这里是status而不是recalculateStatus
        expected: '--'
      }
    ];

    testCases.forEach(({ row, expected }) => {
      expect(getRecalculateRunning(row)).toBe(expected);
    });
  });

  it('运行状态和重算状态应该独立设置', () => {
    const tableData: ProcessTableParam[] = [
      { id: 1, status: 'RUNNING', recalculateStatus: 'STOPPED' },
      { id: 2, status: 'SAVED', recalculateStatus: 'RECALCULATING' },
      { id: 3, status: 'RUNNING', recalculateStatus: 'RECALCULATING' },
      { id: 4, status: 'SUBMITTED' }, // 没有重算状态
    ];

    addRunningColumn(tableData);

    expect(tableData[0].isRunning).toBe(true);
    expect(tableData[0].isRecalculating).toBe(false);

    expect(tableData[1].isRunning).toBe(false);
    expect(tableData[1].isRecalculating).toBe(true);

    expect(tableData[2].isRunning).toBe(true);
    expect(tableData[2].isRecalculating).toBe(true);

    expect(tableData[3].isRunning).toBe(true);
    expect(tableData[3].isRecalculating).toBe(undefined);
  });

  it('重算确认后应该只更新重算状态，保持流程状态不变', () => {
    const row: ProcessTableParam = {
      id: 1,
      status: 'SAVED',
      recalculateStatus: 'STOPPED',
      isRecalculating: false
    };

    const originalStatus = row.status;
    handleRecalculateConfirm(row);

    // 流程状态应该保持不变
    expect(row.status).toBe(originalStatus);

    // 重算状态应该更新
    expect(row.recalculateStatus).toBe('RECALCULATING');
    expect(row.isRecalculating).toBe(true);
    expect(row.recalculateRunningTime).toBeDefined();
  });

  it('取消重算后应该只更新重算状态，保持流程状态不变', () => {
    const row: ProcessTableParam = {
      id: 1,
      status: 'RUNNING',
      recalculateStatus: 'RECALCULATING',
      isRecalculating: true,
      recalculateRunningTime: '2024-01-01 10:00:00'
    };

    const originalStatus = row.status;
    cancelRecalculateTask(row);

    // 流程状态应该保持不变
    expect(row.status).toBe(originalStatus);

    // 重算状态应该更新
    expect(row.recalculateStatus).toBe('STOPPED');
    expect(row.isRecalculating).toBe(false);
    expect(row.recalculateRunningTime).toBe(undefined);
  });

  it('状态独立性验证：流程状态和重算状态互不影响', () => {
    const scenarios = [
      {
        name: '流程运行中，重算停止',
        status: 'RUNNING',
        recalculateStatus: 'STOPPED',
        expectedRunning: true,
        expectedRecalculating: false
      },
      {
        name: '流程停止，重算运行中',
        status: 'SAVED',
        recalculateStatus: 'RECALCULATING',
        expectedRunning: false,
        expectedRecalculating: true
      },
      {
        name: '流程和重算都在运行',
        status: 'RUNNING',
        recalculateStatus: 'RECALCULATING',
        expectedRunning: true,
        expectedRecalculating: true
      }
    ];

    scenarios.forEach(({ name, status, recalculateStatus, expectedRunning, expectedRecalculating }) => {
      const tableData: ProcessTableParam[] = [{ id: 1, status, recalculateStatus }];
      addRunningColumn(tableData);

      expect(tableData[0].isRunning).toBe(expectedRunning);
      expect(tableData[0].isRecalculating).toBe(expectedRecalculating);
    });
  });
});
