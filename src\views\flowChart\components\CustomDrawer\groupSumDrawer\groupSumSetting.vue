<template>
  <div class="page">
    <el-form
      class="form-style"
      ref="ruleFormRef"
      label-position="top"
      require-asterisk-position="right"
      label-width="auto"
      :model="groupSumData"
      :rules="groupSumDataRules"
      :show-message="false"
    >
      <el-form-item label="节点名称" prop="name">
        <el-input v-model="groupSumData.name" maxlength="20"></el-input>
        <div class="form-item-tips">指定任何有意义且能描述本节点的说明</div>
      </el-form-item>
      <el-form-item label="节点ID" prop="id">
        <el-input v-model="groupSumData.id" disabled></el-input>
        <div class="form-item-tips">节点ID不能重复</div>
      </el-form-item>
      <el-form-item label="分组模式" prop="func">
        <el-select v-model="groupSumData.func" @change="groupChange">
          <el-option
            v-for="item in groupOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <div class="form-item-tips">
          指定分组运算模式（最小和最大运算支持数字和时间比较）
        </div>
      </el-form-item>
      <el-form-item label="分组字段" prop="groupBy">
        <div style="display: flex; width: 100%">
          <el-select
            v-model="groupSumData.groupBy"
            filterable
            multiple
            collapse-tags
            collapse-tags-tooltip
            :max-collapse-tags="2"
            @change="selectChange"
          >
            <el-option
              v-for="item in groupByOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              :disabled="isOptionsDisabled(item)"
            />
          </el-select>
          <el-button
            style="margin-left: 4px"
            :icon="RefreshRight"
            @click="loadField"
          >
            载入字段
          </el-button>
        </div>
        <div class="form-item-tips">
          指定要进行分组的字段，输入字段id后回车（必须要有一个时间格式的字段）
        </div>
      </el-form-item>
      <template v-for="(item, index) in dateFieldData">
        <el-form-item
          :label="item.label"
          :prop="'periodField.' + index + '.period'"
          v-if="groupSumData.periodField && isExistDateField"
        >
          <el-radio-group v-model="groupSumData.periodField[index].period">
            <template v-for="item in periodFieldOptions">
              <el-radio :value="item.value">{{ item.label }}</el-radio>
            </template>
          </el-radio-group>
          <div class="form-item-tips">按照周期进行分组</div>
        </el-form-item>
      </template>
      <el-form-item
        label="计算字段ID"
        prop="funcField"
        v-show="isExistFuncField"
      >
        <div style="display: flex; width: 100%">
          <el-select v-model="groupSumData.funcField" filterable>
            <el-option
              v-for="item in groupByOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-button
            style="margin-left: 4px"
            :icon="RefreshRight"
            @click="loadField"
          >
            载入字段
          </el-button>
        </div>
        <div class="form-item-tips">
          进行分组求和、平均值、最小值、最大值时请指定计算字段ID
        </div>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="groupSumData.remark"
          type="textarea"
          :rows="1"
        ></el-input>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { RefreshRight } from "@element-plus/icons-vue";

// 引入通用的校验规则
import {
  getFormRules,
  getConnectData,
  getNodeFrom,
  getResetField,
  getCommonEnumField,
} from "@/utils/common";

// 获取父组件的ref
const props = defineProps<{
  showDrawer: boolean;
  keyMenuItem: string;
}>();

// 获取爷爷的响应式数据
const inputData = inject("inputData") as Ref<EditDrawerParam>;

// 数据监听  -- 通过监听去对弹窗数据进行重置
watch(
  () => props.showDrawer,
  async (val) => {
    if (!val) return;
    // 使用async和await来处理nextTick，减少代码层级
    await nextTick();
    groupSumData.value = cloneDeep(inputData.value);
    groupSumData.value.from ??= [];
    initData();
    refreshOptions();
    // 延迟进行表单校验的清空操作才会生效,处理下拉框的change导致进入弹窗的校验触发
    setTimeout(() => {
      clearValidate();
    }, 10);
  },
  {
    immediate: true,
  }
);

function initData() {
  groupSumData.value.to = groupSumData.value.id;
  groupSumData.value.func ??= 17;
  groupSumData.value.groupBy ??= [];
  if (isEmpty(groupSumData.value.groupBy)) {
    dateFieldData.value.length = 0;
  }
  groupSumData.value.periodField ??= [];
}

// 定义页面名称
const componentPageName = "groupSumSetting";

// 用于监听菜单是否进行切换到指定页面
watch(
  () => props.keyMenuItem,
  (newVal, oldVal) => {
    if (oldVal === componentPageName) {
      flowChartStore.updateTempData(
        inputData.value.nodeType,
        groupSumData.value
      );
    }
  }
);

// 初始默认值
const groupSumData = ref<EditDrawerParam>({
  id: "",
  name: "",
  groupBy: [],
  periodField: [],
});

import type { FormInstance, FormRules } from "element-plus";

const ruleFormRef = ref<FormInstance>();

const groupSumDataRules = reactive<FormRules<EditDrawerParam>>({
  id: [
    {
      required: true,
      trigger: ["blur", "change"],
    },
  ],
  func: [
    {
      required: true,
      trigger: ["blur", "change"],
    },
  ],
  groupBy: [
    {
      required: true,
      trigger: ["blur", "change"],
    },
  ],
});

// 分组模式
const groupOptions = ref<OptionsParam[]>([
  {
    value: 17,
    label: "标准Groupby分组（记录数）",
  },
  {
    value: 18,
    label: "Groupby分组并对指定字段求和",
  },
  {
    value: 11,
    label: "Groupby分组并对指定字段求平均值",
  },
  {
    value: 3,
    label: "Groupby分组并对指定字段求最大值",
  },
  {
    value: 4,
    label: "Groupby分组并对指定字段求最小值",
  },
]);

function groupChange(value: string | number) {
  if (value === 17) {
    groupSumData.value.funcField = "";
  }
}

// 数据所在节点
const locatedNodeOptions = ref<ComputerOptionsParam[]>([]);

import { useVueFlow } from "@vue-flow/core";

const { getIncomers } = useVueFlow();

// 分组字段
const groupByOptions = ref<NodeOptionsParam[]>([]);

import { useflowChartStore } from "@/store/modules/flowChart";
const flowChartStore = useflowChartStore();

// 回显判断是否重新获取字段信息
async function refreshOptions() {
  getlocatedData();
  if (!isEmpty(groupSumData.value.groupBy)) {
    await loadField();
    dateFieldData.value = cloneDeep(groupSumData.value.periodField) || [];
  } else {
    groupByOptions.value.length = 0;
  }
}

// 获取与之连接数据连接
function getlocatedData() {
  locatedNodeOptions.value.length = 0;
  // 获取当前节点的所有入边
  const incomers = getIncomers(inputData.value.id);
  locatedNodeOptions.value = getResetField(incomers);
}

async function loadField() {
  groupByOptions.value.length = 0;
  // 调用方法获取字段数据
  const resData = (await getConnectData(
    locatedNodeOptions.value
  )) as ConnectDataParam;
  const { getTableData, filterData } = resData;
  groupSumData.value.dataStream = filterData?.dataStream;
  if (isEmpty(getTableData)) return;
  groupSumData.value.joinTable = getTableData;
  groupByOptions.value = getCommonEnumField(getTableData);
}

function selectChange(value: string[]) {
  dateFieldData.value.length = 0;
  if (value.length == 0) return;
  const oldPeriod = groupSumData.value.periodField;
  value.forEach((item: string) => {
    const option = groupByOptions.value.find(
      (option) => option.value === item && option.key === "date"
    );
    if (option && option.value) {
      const curPeriod = oldPeriod?.find((key) => key.value === option.value);
      dateFieldData.value.push({
        value: option.value,
        label: option.value + "分组",
        key: option.key,
        period: curPeriod?.period || "",
      });
    }
  });
  groupSumData.value.periodField = cloneDeep(dateFieldData.value);
}
const dateFieldData = ref<DateFieldParam[]>([]);

// 进行限制处理，只允许选择一个日期字段
function isOptionsDisabled(data: NodeOptionsParam) {
  if (dateFieldData.value.length == 0) return false;
  return dateFieldData.value[0].value !== data.value && data.key == "date";
}

// 遍历多个日期字段，进行校验
function dateFieldChange() {
  dateFieldData.value.forEach((item, index) => {
    groupSumDataRules[`periodField.${index}.period`] = [
      {
        required: true,
        trigger: ["blur", "change"],
      },
    ];
  });
}
// 判断是否存在日期字段
const isExistDateField = computed(() => {
  dateFieldChange();
  return dateFieldData.value.length > 0;
});

// 计算字段进行校验
function funcChange() {
  groupSumDataRules["funcField"] =
    groupSumData.value.func !== 17
      ? [
          {
            required: true,
            trigger: ["blur", "change"],
          },
        ]
      : [
          {
            required: false,
            trigger: ["blur", "change"],
          },
        ];
}

const isExistFuncField = computed(() => {
  funcChange();
  return groupSumData.value.func !== 17;
});

// 周期选择字段
const periodFieldOptions: OptionsParam[] = [
  {
    value: 17,
    label: "年",
  },
  {
    value: 15,
    label: "季",
  },
  {
    value: 14,
    label: "月",
  },
  {
    value: 13,
    label: "周",
  },
  {
    value: 12,
    label: "日",
  },
  {
    value: 7,
    label: "小时",
  },
];

async function getNewData() {
  const filedType = groupByOptions.value.find(
    (option) => option.value === groupSumData.value.funcField
  )?.key;
  if (
    filedType !== "number" &&
    groupSumData.value.func &&
    [3, 4, 11].includes(Number(groupSumData.value.func))
  ) {
    ElMessage.error("计算字段非数值类型字段不支持平均值、最大值、最小值");
    return false;
  }
  if (groupSumData.value.periodField?.length == 0) {
    ElMessage.error("分组字段中必须包含时间格式字段");
    return false;
  }
  groupSumData.value.from = getNodeFrom(locatedNodeOptions.value);
  const childRuleBool = await getFormRules(
    ruleFormRef.value,
    inputData.value.nodeType,
    groupSumData.value,
    "基本配置"
  );
  return childRuleBool;
}

// 判断输入源是否修改导致字段进行修改
function checkFieldUpdate() {
  if (!inputData.value.func) return false;
  if (groupSumData.value.func !== inputData.value.func) {
    return true;
  }
  if (isEmpty(inputData.value.groupBy)) return false;
  return !isEqual(groupSumData.value.groupBy, inputData.value.groupBy);
}

// 关闭弹窗清楚校验
function clearValidate() {
  ruleFormRef.value?.clearValidate();
}

// 放开方法给父组件使用
defineExpose({
  getNewData,
  checkFieldUpdate,
});
</script>

<style lang="scss" scoped>
.page {
  height: 100%;
}
</style>
