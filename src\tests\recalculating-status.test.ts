import { describe, it, expect } from 'vitest'

describe('RECALCULATING Status Handling', () => {
  // 模拟状态配置
  const STATUS_COLOR: Record<string, string> = {
    RUNNING: "success",
    FAILED: "fail",
    CANCELED: "warning",
    SUBMITTED: "0",
    CANCELLING: "1",
    RECALCULATING: "success",
    ADDED: "2",
    UPDATED: "3",
    SAVED: "4",
    DELETED: "5",
  }

  const RUNNING_STATUS = ["RUNNING", "SUBMITTED", "RECALCULATING"]
  const DISABLE_STATUS_DATA = ["RUNNING", "CANCELLING", "SUBMITTED", "RECALCULATING"]
  const DISABLED_DATA = ["CANCELLING", "SUBMITTED", "RECALCULATING"]

  // 模拟流程数据
  interface ProcessData {
    id: number
    status: string
    isRunning?: boolean
  }

  // 模拟addRunningColumn函数
  function addRunningColumn(tableData: ProcessData[]) {
    tableData.forEach((item: ProcessData) => {
      if (item.status) {
        item.isRunning = RUNNING_STATUS.includes(item.status)
      }
    })
  }

  // 模拟状态颜色获取函数
  function getStatusColor(status: string) {
    return "tag-status-" + STATUS_COLOR[status] || "success"
  }

  // 模拟禁用检查函数
  function isDisabled(status: string) {
    return DISABLE_STATUS_DATA.includes(status)
  }

  function isRunningDisabled(status: string) {
    return DISABLED_DATA.includes(status)
  }

  it('should include RECALCULATING in status color mapping', () => {
    expect(STATUS_COLOR.RECALCULATING).toBe('success')
    expect(getStatusColor('RECALCULATING')).toBe('tag-status-success')
  })

  it('should set isRunning to true when status is RECALCULATING', () => {
    const testData: ProcessData[] = [
      { id: 1, status: 'RUNNING' },
      { id: 2, status: 'RECALCULATING' },
      { id: 3, status: 'FAILED' },
      { id: 4, status: 'SUBMITTED' }
    ]

    addRunningColumn(testData)

    expect(testData[0].isRunning).toBe(true)  // RUNNING
    expect(testData[1].isRunning).toBe(true)  // RECALCULATING - 这是我们要测试的
    expect(testData[2].isRunning).toBe(false) // FAILED
    expect(testData[3].isRunning).toBe(true)  // SUBMITTED
  })

  it('should disable buttons when status is RECALCULATING', () => {
    expect(isDisabled('RECALCULATING')).toBe(true)
    expect(isDisabled('RUNNING')).toBe(true)
    expect(isDisabled('FAILED')).toBe(false)
    expect(isDisabled('SAVED')).toBe(false)
  })

  it('should disable running switch when status is RECALCULATING', () => {
    expect(isRunningDisabled('RECALCULATING')).toBe(true)
    expect(isRunningDisabled('CANCELLING')).toBe(true)
    expect(isRunningDisabled('RUNNING')).toBe(false)
    expect(isRunningDisabled('FAILED')).toBe(false)
  })

  it('should handle RECALCULATING status in all status arrays', () => {
    // 检查RECALCULATING是否在所有相关的状态数组中
    expect(RUNNING_STATUS).toContain('RECALCULATING')
    expect(DISABLE_STATUS_DATA).toContain('RECALCULATING')
    expect(DISABLED_DATA).toContain('RECALCULATING')
  })

  it('should maintain consistency between table and card layouts', () => {
    // 确保表格和卡片布局使用相同的状态配置
    const tableStatusColor = STATUS_COLOR.RECALCULATING
    const cardStatusColor = STATUS_COLOR.RECALCULATING
    
    expect(tableStatusColor).toBe(cardStatusColor)
    expect(tableStatusColor).toBe('success')
  })

  it('should handle status update after recalculate confirmation', () => {
    const mockRow: ProcessData = {
      id: 1,
      status: 'SAVED',
      isRunning: false
    }

    // 模拟重算确认后的状态更新
    mockRow.status = 'RECALCULATING'
    mockRow.isRunning = true

    expect(mockRow.status).toBe('RECALCULATING')
    expect(mockRow.isRunning).toBe(true)
    expect(getStatusColor(mockRow.status)).toBe('tag-status-success')
    expect(isDisabled(mockRow.status)).toBe(true)
  })
})
