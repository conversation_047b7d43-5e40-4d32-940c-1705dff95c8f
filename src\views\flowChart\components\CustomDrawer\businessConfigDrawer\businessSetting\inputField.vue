<template>
  <div class="page page-table">
    <el-form
      class="form-style form-table-style"
      ref="ruleFormRef"
      :model="inputOperatorData"
      :rules="inputOperatorDataRules"
      :show-message="false"
      label-width="auto"
    >
      <el-form-item prop="tableData" class="form-table-height">
        <el-table :data="tableData" class="fullheight" scrollbar-always-on>
          <template v-for="(item, index) in fieldTableHeader" :key="index">
            <el-table-column
              v-bind="item"
              show-overflow-tooltip
            ></el-table-column>
          </template>
        </el-table>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
// 引入通用的校验规则
import { getFormRules, nodeTypeFieldMap } from "@/utils/common";

// 获取父组件的ref
const props = defineProps<{
  showDrawer: boolean;
  stepActive: number;
}>();

// 获取爷爷的响应式数据
const inputData = inject("inputData") as Ref<AllInputParam>;

// 数据监听  -- 通过监听去对弹窗数据进行重置
watch(
  () => props.showDrawer,
  async (val) => {
    if (!val) return;
    // 使用async和await来处理nextTick，减少代码层级
    await nextTick();
    inputOperatorData.value.id = inputData.value.id;
  },
  {
    immediate: true,
  }
);

const inputOperatorData = ref<AllInputParam>({
  id: "",
});

import { fieldTableHeader } from "@/utils/globalField";

const tableData = ref<FieldTableParam[]>([]);

// 监听步骤发现改变获取最新的数据
watch(
  () => props.stepActive,
  (val) => {
    if (val === 2) {
      queryFieldTableData(inputData.value.type);
    }
  }
);

// 获取表格字段数据
async function queryFieldTableData(type?: string) {
  if (!type) return;
  // 查询字段表数据
  const getTableData = await nodeTypeFieldMap(type, inputData.value);
  tableData.value = cloneDeep(getTableData);
}

import type { FormInstance } from "element-plus";

const ruleFormRef = ref<FormInstance>();

const inputOperatorDataRules = {
  tableData: [
    {
      required: false,
      trigger: ["blur", "change"],
    },
  ],
};

async function getNewData() {
  const childRuleBool = await getFormRules(
    ruleFormRef.value,
    inputData.value.nodeType,
    inputOperatorData.value,
    "输入字段"
  );
  return childRuleBool;
}

// 放开方法给父组件使用
defineExpose({
  getNewData,
});
</script>

<style lang="scss" scoped>
.page {
  height: calc(100% - 80px);
}
</style>
