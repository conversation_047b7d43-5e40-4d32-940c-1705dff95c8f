# 重算功能测试指南

## 功能概述
✅ **已完成实现**：在蓝云计算引擎的processList页面新增了重算功能，包括：
1. ✅ 表格布局中的重算按钮（橙色"重算"文字按钮）
2. ✅ 卡片布局中的重算按钮（橙色刷新图标）
3. ✅ 重算弹窗，包含开始时间和结束时间选择
4. ✅ API接口：`/flink-job/recalculate`
5. ✅ TypeScript类型定义完整
6. ✅ 表单验证和错误处理

## 测试步骤

### 1. 访问页面
- 打开浏览器访问：http://localhost:9560/
- 导航到流程列表页面

### 2. 表格布局测试
- 确保页面处于表格布局模式
- 查看操作列是否显示"重算"按钮
- 点击任意一行的"重算"按钮

### 3. 卡片布局测试
- 切换到卡片布局模式
- 查看卡片右上角是否有重算图标（橙色刷新图标）
- 点击重算图标

### 4. 重算弹窗测试
- 验证弹窗标题为"重算设置"
- 验证开始时间字段为必填项
- 验证结束时间默认为当天00:00
- 测试时间选择功能
- 测试表单验证（开始时间不能晚于结束时间）
- 点击"启动"按钮提交

### 5. API调用测试
- 打开浏览器开发者工具
- 查看网络请求是否正确发送到 `/flink-job/recalculate`
- 验证请求参数包含：id, jobName, startTime, endTime

## 预期结果
1. 重算按钮正确显示在表格和卡片中
2. 弹窗正确显示并包含所需字段
3. 表单验证正常工作
4. API请求正确发送
5. 成功提示消息显示
6. 列表自动刷新

## 注意事项
- 重算按钮的禁用状态与编辑、删除按钮保持一致
- 弹窗关闭时会重置表单数据
- 时间格式为：YYYY-MM-DD HH:mm:ss

## 已修复的问题
✅ **TypeScript类型错误**：修复了`defineEmits`的类型定义格式
✅ **编译错误**：所有TypeScript类型检查通过
✅ **开发服务器**：成功启动在 http://localhost:9560/

## 实现的文件清单
1. ✅ `src/api/processListApi/types.ts` - 添加重算接口类型
2. ✅ `src/api/processListApi/index.ts` - 添加重算API接口
3. ✅ `src/views/processList/components/recalculateDialog.vue` - 重算弹窗组件
4. ✅ `src/views/processList/components/tableLayout.vue` - 表格布局添加重算按钮
5. ✅ `src/views/processList/components/cardLayout.vue` - 卡片布局添加重算按钮
6. ✅ `src/views/processList/index.vue` - 主页面集成重算功能

## 功能特性
- 🎯 **开始时间必选**：表单验证确保开始时间不能为空
- 🕐 **结束时间默认值**：自动设置为当天00:00
- ⚡ **时间验证**：开始时间不能晚于结束时间
- 🔄 **自动刷新**：重算成功后自动刷新流程列表
- 💬 **用户反馈**：成功/失败消息提示
- 🎨 **UI一致性**：按钮样式与现有设计保持一致
