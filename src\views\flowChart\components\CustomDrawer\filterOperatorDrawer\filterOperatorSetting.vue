<template>
  <div class="page page-table">
    <el-form
      class="form-style form-table-style"
      ref="ruleFormRef"
      label-position="top"
      require-asterisk-position="right"
      label-width="auto"
      :model="filterOperatorData"
      :rules="filterOperatorDataRules"
      :show-message="false"
    >
      <el-form-item label="节点名称" prop="name">
        <el-input v-model="filterOperatorData.name" maxlength="20"></el-input>
        <div class="form-item-tips">指定任何有意义且能描述本节点的说明</div>
      </el-form-item>
      <el-form-item label="节点ID" prop="id">
        <el-input v-model="filterOperatorData.id" disabled></el-input>
        <div class="form-item-tips">节点ID不能重复</div>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="filterOperatorData.remark"
          type="textarea"
          :rows="1"
        ></el-input>
      </el-form-item>
      <el-form-item label="数据需要满足的条件" prop="totalFilter">
        <el-select v-model="filterOperatorData.totalFilter">
          <el-option label="所有" value="&&"></el-option>
          <el-option label="任一" value="||"></el-option>
        </el-select>
        <div class="form-item-tips">请选择一个满足条件</div>
      </el-form-item>
      <el-form-item prop="filterConditionArray" class="form-table-height">
        <div class="filter-main">
          <el-dropdown
            trigger="click"
            :hide-on-click="false"
            @command="handleCommand"
            @visible-change="visibleChange"
          >
            <span class="el-dropdown-link">+ 添加过滤条件</span>
            <template #dropdown>
              <el-dropdown-menu style="width: 250px" max-height="300px">
                <el-input
                  type="text"
                  v-model="inputName"
                  placeholder="搜索"
                  :prefix-icon="Search"
                  style="padding: 8px 12px 12px 12px"
                  @input="changeName"
                />
                <div
                  style="max-height: 200px; overflow-y: auto; overflow-x: auto"
                >
                  <template v-for="item in filterFieldData">
                    <el-dropdown-item
                      :command="item.value"
                      :disabled="isDisabled(item.value)"
                    >
                      {{ item.label }}
                    </el-dropdown-item>
                  </template>
                </div>
                <div
                  v-show="filterFieldData.length == 0"
                  style="
                    padding: 10px 0;
                    margin: 0;
                    text-align: center;
                    color: #a3a6ad;
                  "
                >
                  <span>No data</span>
                </div>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-button
            v-show="isShowDeleteAll"
            type="danger"
            @click="deleteAllData"
            link
            :icon="Delete"
          >
            删除全部
          </el-button>
        </div>
        <div class="filter-content">
          <template
            v-for="(item, index) in filterOperatorData.filterConditionArray"
          >
            <div class="content-item">
              <!-- 过滤条件字段 -->
              <el-select
                v-model="item.selectField"
                placeholder="请选择"
                style="width: 160px"
                clearable
                @change="changeField(item.selectField, index)"
              >
                <el-option
                  v-for="key in initSelectData"
                  :key="key.value"
                  :label="key.label"
                  :value="key.value"
                  :disabled="isDisabled(key.value)"
                />
              </el-select>
              <!-- 过滤条件方法 -->
              <el-select
                v-model="item.selectFunctionName"
                class="select-noBorder"
                :disabled="!item.selectField"
                @change="changeFunction(item.selectFunctionName, index)"
              >
                <el-option
                  v-for="key in getFunctionData(item.selectField)"
                  :key="key.value"
                  :label="key.label"
                  :value="key.value"
                />
              </el-select>
              <!-- 过滤方法规则 -->
              <el-select
                v-model="item.rangeType"
                v-show="isShowRange(item.selectFunctionName)"
                style="width: 100px; margin-right: 4px"
              >
                <el-option
                  v-for="key in rangeData"
                  :key="key.value"
                  :label="key.label"
                  :value="key.value"
                />
              </el-select>
              <!-- 过滤值组件 -->
              <div class="component-style" style="flex: 1">
                <component
                  :is="
                    getCurComponent(
                      getFunctionData(item.selectField),
                      item.selectFunctionName,
                      'component'
                    )
                  "
                  v-model="item.inputValue"
                  v-bind="
                    getCurComponent(
                      getFunctionData(item.selectField),
                      item.selectFunctionName,
                      'props'
                    )
                  "
                >
                  <template v-if="isShowEnum(item.selectField)">
                    <el-option
                      v-for="key in enumDataMap[item.selectField]"
                      :key="key.value"
                      :label="key.label"
                      :value="key.value"
                    />
                  </template>
                </component>
              </div>
              <!-- 删除按钮 -->
              <div class="delete-icon" @click="deleteItemData(index)">
                <el-icon :size="16"><Delete /></el-icon>
              </div>
            </div>
          </template>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { Search, Delete } from "@element-plus/icons-vue";

// 引入通用的校验规则
import {
  getFormRules,
  getConnectData,
  getNodeFrom,
  getAllTitle,
  getResetField,
  getCommonEnumField,
} from "@/utils/common";

const props = defineProps<{
  showDrawer: boolean;
  keyMenuItem: string;
}>();

// 获取爷爷的响应式数据
const inputData = inject("inputData") as Ref<EditDrawerParam>;

// 数据监听  -- 通过监听去对弹窗数据进行重置
watch(
  () => props.showDrawer,
  async (val) => {
    if (!val) return;
    // 使用async和await来处理nextTick，减少代码层级
    await nextTick();
    filterOperatorData.value = cloneDeep(inputData.value);
    filterOperatorData.value.from ??= [];
    initData();
    refreshOptions();
  },
  {
    immediate: true,
  }
);

function initData() {
  filterOperatorData.value.to = filterOperatorData.value.id;
  isClickCommand.value.length = 0;
  filterOperatorData.value.totalFilter ||= "&&";
  filterOperatorData.value.filterConditionArray ??= [];
  filterOperatorData.value.filterTable ??= [];
}

const filterOperatorData = ref<EditDrawerParam>({
  id: "",
  name: "",
});

import { useflowChartStore } from "@/store/modules/flowChart";
const flowChartStore = useflowChartStore();

// 定义页面名称
const componentPageName = "filterOperatorSetting";

// 用于监听菜单是否进行切换到指定页面
watch(
  () => props.keyMenuItem,
  (newVal, oldVal) => {
    if (oldVal === componentPageName) {
      flowChartStore.updateTempData(
        inputData.value.nodeType,
        filterOperatorData.value
      );
    }
  }
);

// 表单校验规则
import type { FormInstance } from "element-plus";

const ruleFormRef = ref<FormInstance>();

const filterOperatorDataRules = {
  id: [
    {
      required: true,
      trigger: ["blur", "change"],
    },
  ],
  totalFilter: [
    {
      required: true,
      trigger: ["blur", "change"],
    },
  ],
};

import { useVueFlow } from "@vue-flow/core";

const { getIncomers } = useVueFlow();

// 数据所在节点
const locatedNodeOptions = ref<ComputerOptionsParam[]>([]);

// 回显判断是否重新获取字段信息
async function refreshOptions() {
  getlocatedData();
  if (!isEmpty(filterOperatorData.value.filterConditionArray)) {
    await loadField();
    getIsClickCommand();
  }
}

// 获取与之连接数据连接
function getlocatedData() {
  locatedNodeOptions.value.length = 0;
  // 获取当前节点的所有入边
  const incomers = getIncomers(inputData.value.id);
  locatedNodeOptions.value = getResetField(incomers);
}

// 获取已选择的字段信息
function getIsClickCommand() {
  isClickCommand.value =
    filterOperatorData.value.filterConditionArray?.map(
      (item) => item.selectField
    ) || [];
}

const initSelectData = ref<NodeOptionsParam[]>([]);

const filterFieldData = ref<NodeOptionsParam[]>([]);

async function loadField() {
  initSelectData.value.length = 0;
  filterFieldData.value.length = 0;
  // 调用方法获取字段数据
  const resData = (await getConnectData(
    locatedNodeOptions.value
  )) as ConnectDataParam;
  const { getTableData, filterData } = resData;
  filterOperatorData.value.dataStream = filterData?.dataStream;
  if (isEmpty(getTableData)) return;
  filterOperatorData.value.filterTable = cloneDeep(getTableData);
  initSelectData.value = getCommonEnumField(getTableData);
  filterFieldData.value = cloneDeep(initSelectData.value);
}

const isClickCommand = ref<(string | number)[]>([]);

// 下拉菜单点击事件
function handleCommand(val: string | number) {
  isClickCommand.value.push(val);
  const name = getDefaultMethod(val);
  filterOperatorData.value.filterConditionArray?.push({
    selectField: val,
    selectFunctionName: name,
    inputValue: getDefaultValue(val, name),
    rangeType: getDefaultRule(name),
  });
}

const whiteMethods = ref<(string | number)[]>(["in", "notIn", "range"]);

// 获取过滤规则的默认值
function getDefaultValue(val: string | number, name: string) {
  const type = initSelectData.value.find((item) => item.value === val)?.key;
  // 先判断是否是数值类型，转为nulll
  const defaultVal1 = type === "number" ? null : "";
  // 再判断是否是whitleMethods中的数组类型，转为[]
  const defaultVal2 = whiteMethods.value.includes(name) ? [] : defaultVal1;
  return defaultVal2;
}

// 获取过滤方法的默认值
function getDefaultMethod(val: string | number) {
  const type = initSelectData.value.find((item) => item.value === val)?.key;
  if (type === "string") return "contains";
  if (type === "enum") {
    getEnumData(val);
    return "in";
  }
  return "eq";
}

const enumData = ref<OptionsParam[]>([]);

// 接口导入
import customApi from "@/api/custom";

interface enumParam {
  [key: string]: OptionsParam[];
}

const enumDataMap = ref<enumParam>({});

// 获取下拉框的枚举值
function getEnumData(val: string | number) {
  enumData.value.length = 0;
  enumDataMap.value[val] = [];
  const type =
    filterOperatorData.value.filterTable?.find((item) => item.field === val)
      ?.fieldType || "";
  customApi.queryEnumerationsData({ id: type }).then((res) => {
    enumData.value = cloneDeep(res.data);
    enumDataMap.value[val] = cloneDeep(enumData.value);
  });
}

// 获取左右过滤规则的默认值(默认左闭右闭)
function getDefaultRule(val: string) {
  return val === "range" ? "lcrc" : "";
}

function isDisabled(value: string | number) {
  return isClickCommand.value.includes(value);
}

async function visibleChange(val: boolean) {
  // 重新打开下拉菜单的时候，清空数据
  if (val) {
    await loadField();
    inputName.value = "";
    filterFieldData.value = initSelectData.value;
  }
}

const inputName = ref<string>("");

// 添加过滤条件 -- 搜索框
function changeName(val: string) {
  if (!val) {
    filterFieldData.value = initSelectData.value;
    return;
  }
  filterFieldData.value = filterFieldData.value.filter((item) => {
    return item.label.includes(val);
  });
}

const isShowDeleteAll = computed(() => {
  return !isEmpty(filterOperatorData.value.filterConditionArray);
});

// 清除全部过滤条件
function deleteAllData() {
  ElMessageBox.confirm("确定要清空所有过滤条件吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      // 清空过滤条件
      isClickCommand.value.length = 0;
      filterOperatorData.value.filterConditionArray = [];
    })
    .catch(() => {
      ElMessage.warning("取消删除");
    });
}

// 每一行中 -- 字段改变
function changeField(val: string | number, index: number) {
  // 更改过滤字段
  isClickCommand.value.splice(index, 1, val);
  const name = getDefaultMethod(val);
  const param = {
    selectField: val,
    selectFunctionName: name,
    inputValue: getDefaultValue(val, name),
    rangeType: getDefaultRule(name),
  };
  filterOperatorData.value.filterConditionArray?.splice(index, 1, param);
}

// 每一行中 -- 单个删除
function deleteItemData(index: number) {
  filterOperatorData.value.filterConditionArray?.splice(index, 1);
  isClickCommand.value.splice(index, 1);
}

// 导入过滤方法模型定义
import { filterMethodOptions, filterValueOptions } from "@/utils/globalField";

// 每一行中 -- 根据字段类型获取过滤方法
function getFunctionData(val: string | number) {
  const type = initSelectData.value.find((item) => item.value === val)?.key;
  if (!type) return [];
  return filterMethodOptions[type];
}

// 每一行中 -- 更改过滤方法
function changeFunction(value: string, index: number) {
  const data = filterOperatorData.value.filterConditionArray;
  if (!data) return;
  data[index].inputValue = getDefaultValue(data[index].selectField, value);
  data[index].rangeType = getDefaultRule(value);
}

const rangeData = ref<OptionsParam[]>([
  {
    value: "lcrc", // 字段的唯一键
    label: "左闭右闭", // 字段名称
  },
  {
    value: "lcro",
    label: "左闭右开",
  },
  {
    value: "lorc",
    label: "左开右闭",
  },
  {
    value: "loro",
    label: "左开右开",
  },
]);

function isShowRange(val: string) {
  return val === "range";
}

// 每一行中 -- 根据选择的方法获取当前对应的组件
function getCurComponent(
  data: FilterComponentParam[],
  value: string,
  type: string
) {
  const curComponent = data?.find(
    (item: FilterComponentParam) => item.value === value
  )?.componentName;
  if (!curComponent) return;
  return filterValueOptions[curComponent][type];
}

// 每一行中 -- 判断是否展示下拉框
function isShowEnum(val: string | number) {
  const type = initSelectData.value.find((item) => item.value === val)?.key;
  return type == "enum";
}

// 进行保存校验
async function getNewData() {
  if (!conditionCheck()) {
    return false;
  }
  getfilterCondition();
  filterOperatorData.value.from = getNodeFrom(locatedNodeOptions.value);
  const childRuleBool = await getFormRules(
    ruleFormRef.value,
    inputData.value.nodeType,
    filterOperatorData.value,
    "基本配置"
  );
  return childRuleBool;
}

// 进行过滤条件的表单校验
function conditionCheck() {
  const data = filterOperatorData.value.filterConditionArray?.filter((item) => {
    return (
      ["null", "notNull"].includes(item.selectFunctionName) ||
      !isEmpty(item.inputValue) ||
      (typeof item.inputValue == "number" && item.inputValue)
    );
  });
  if (!data || data.length === 0) {
    ElMessage.error("请至少添加一条过滤条件");
    return false;
  }
  // 去除已删除的字段
  const isClick: (string | number)[] = [];

  for (const item of data) {
    isClick.push(item.selectField);
    if (
      item.selectFunctionName == "range" &&
      item.rangeType &&
      Array.isArray(item.inputValue) &&
      !bigNumberCompare(item)
    ) {
      ElMessage.error(`${item.selectField}选择范围存在无效的区间`);
      return false;
    }
  }
  isClickCommand.value = cloneDeep(isClick);
  filterOperatorData.value.filterConditionArray = cloneDeep(data);
  return true;
}

import BigNumber from "bignumber.js";

// 引入bigNumber进行大精度数据的比较
function bigNumberCompare(item: FilterConditionArrayParam) {
  if (!item.inputValue || !Array.isArray(item.inputValue)) return false;
  let bigNum1 = new BigNumber(item.inputValue[0]);
  let bigNum2 = new BigNumber(item.inputValue[1]);
  let comparisonResult = bigNum1.comparedTo(bigNum2);
  return comparisonResult < 0;
}

function getfilterCondition() {
  const data = filterOperatorData.value.filterConditionArray;
  const keyType = filterOperatorData.value.totalFilter || "";
  if (isEmpty(data)) {
    filterOperatorData.value.filterCondition = "";
    return;
  }
  filterOperatorData.value.filterCondition = data
    ?.map((item, index) => {
      if (item.selectField) {
        const title = index == 0 ? "" : keyType;
        // 获取当前字段的类型
        const fieldType = initSelectData.value.find(
          (key) => key.value === item.selectField
        )?.key;
        return getAllTitle(title, item, fieldType);
      }
    })
    .join("  ");
}

// 放开方法给父组件使用
defineExpose({
  getNewData,
});
</script>

<style lang="scss" scoped>
.page {
  height: 100%;
  .form-table-height {
    height: calc(100% - 390px);
  }
  .filter-header {
    padding: 6px 20px;
    border-left: 1px solid #606266;
  }
  .filter-main {
    width: 100%;
    display: flex;
    justify-content: space-between;
    .el-dropdown-link {
      cursor: pointer;
      color: var(--el-color-primary);
      width: 220px;
    }
  }
  .filter-content {
    height: calc(100% - 32px);
    margin-top: 8px;
    overflow: auto;
    width: 100%;
    padding-right: 8px;
    .content-item {
      display: flex;
      margin-bottom: 12px;
      .delete-icon {
        height: 24px;
        width: 24px;
        text-align: center;
        border-radius: 4px;
        margin-top: 4px;
        margin-left: 8px;
        cursor: pointer;
      }
      .delete-icon:hover {
        background-color: var(--el-fill-color-darker);
      }
      .select-noBorder {
        margin: 0px 8px;
        width: 120px;
      }
      .component-style {
        :deep(.el-input-number .el-input__inner) {
          text-align: left;
        }
      }
    }
  }
}
</style>
