// tsconfig.json
{
  "compilerOptions": {
      "target": "ESNext",
      "useDefineForClassFields": true,
      "module": "ESNext",
      "moduleResolution": "Node",
      "strict": true,
      "jsx": "preserve",
      "resolveJsonModule": true,
      "isolatedModules": true,
      "esModuleInterop": true,
      "lib": ["ESNext", "DOM"],
      "skipLibCheck": true,
      "noEmit": true,
      "allowSyntheticDefaultImports": true, 
      "composite": true,
      // 加入以下配置项
      "baseUrl": ".", // 用于设置解析非相对模块名称的基本目录，相对模块不会受到baseUrl的影响
      "paths": { // 用于设置模块名到基于baseUrl的路径映射
          "@/*": ["src/*"]
      }
  },
  "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"],
}