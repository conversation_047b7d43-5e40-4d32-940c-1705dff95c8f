import type { Node, Edge, Position, ViewportTransform } from "@vue-flow/core";

// 接口入参参数定义
export interface QueryArgusParam {
  id: string | number;
}

export interface SaveFlinkJobParam {
  id: string | number;
  argues?: string;
}

export interface UpdateLayoutParam {
  id: string | number;
  jobName: string;
  layout?: string;
}

export interface LayoutParam {
  nodes: Node[];
  edges: Edge[];
  position: [x: number, y: number];
  viewport: ViewportTransform;
  zoom: number;
}

// 返回值类型定义

export interface ResponseParam {
  jobId: string;
  jobName: string;
}

export interface QueryLayoutResponse extends ResponseParam {
  layout?: string;
}

export interface QueryArgusResponse extends ResponseParam {
  argues?: string;
}

export interface QueryFieldParam {
  functionType: string;
}

// join连接算子 -- 入参
export interface QueryJoinParam {
  associationCondition: string;
  joinMode: string;
  streamA: string;
  streamB: string;
  tableA: FieldTableParam[];
  tableB: FieldTableParam[];
}

// 分组汇总算子 -- 入参
export interface QueryGroupParam {
  id: string | number;
  func: string | number;
  funcField?: string;
  groupBy: string[];
  joinTable: FieldTableParam[];
  periodField?: DateFieldParam[];
}

// 调试查询入参
export interface QueryDebugParam {
  id: string | number;
  jobName: string;
  index: number;
  limit: number;
}

// 调试返回值类型定义
export interface QueryDebugResponse extends CommonDataResponse<any[]> {
  total: number;
}

// 异常处理算子 -- 处理后接口入参
export interface QueryExceptionParam {
  id: string | number;
  businessScene: string;
  dynamicField: DynamicFieldParam;
  upstreamFieldTable: FieldTableParam[];
}

// 异常处理算子 -- 非必填参数返回
export interface abnormalOptionParam extends FieldTableParam {
  // 默认值
  defaultValue?: string | number | boolean;
}
