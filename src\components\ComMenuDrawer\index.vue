<template>
  <el-drawer
    v-model="showDrawer"
    :size="size"
    title="编辑"
    class="drawer-menu-class"
  >
    <template #default>
      <div class="drawer-content">
        <el-menu :default-active="selectMenuItem" @select="handleMenuSelect">
          <template v-for="item in leftMenuList">
            <el-sub-menu v-if="item.type === 'subMenu'" :index="item.label">
              <template #title>
                <svg-icon :name="item.icon"></svg-icon>
                <el-tooltip
                  class="box-item"
                  :content="item.label"
                  placement="bottom"
                >
                  <span class="tool-span">{{ item.label }}</span>
                </el-tooltip>
              </template>
              <template v-for="child in item.children">
                <el-menu-item :index="child.key">
                  <el-tooltip
                    class="box-item"
                    :content="child.label"
                    placement="bottom"
                  >
                    <span class="tool-span">{{ child.label }}</span>
                  </el-tooltip>
                </el-menu-item>
              </template>
            </el-sub-menu>
            <el-menu-item :index="item.key" v-else>
              <svg-icon :name="item.icon"></svg-icon>
              <el-tooltip
                class="box-item"
                :content="item.label"
                placement="bottom"
              >
                <span class="tool-span">{{ item.label }}</span>
              </el-tooltip>
            </el-menu-item>
          </template>
        </el-menu>
        <div class="right-content">
          <slot></slot>
        </div>
      </div>
    </template>
    <template #footer>
      <div style="flex: auto">
        <el-button @click="showDrawer = false">关闭</el-button>
        <el-button
          @click="emit('previousStep')"
          v-show="isStepShow && stepActive > 0"
        >
          上一步
        </el-button>
        <el-button @click="emit('nextStep')" v-show="isStepShow && justPageNum">
          下一步
        </el-button>
        <el-button
          type="primary"
          @click="emit('updateNodeInfo')"
          v-show="isStepShowConfime"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
// 获取父组件的ref
const props = withDefaults(
  defineProps<{
    leftMenuList?: MenuItemParam[];
    selectMenuItem?: string; // 当前选中的菜单
    size?: string | number; // 菜单抽屉的宽度
    showConfirm?: boolean; // 是否显示确认按钮
  }>(),
  {
    size: 800,
    showConfirm: true,
  }
);

// 传递子组件的emit
const emit = defineEmits([
  "updateSelect",
  "updateNodeInfo",
  "previousStep",
  "nextStep",
]);

// 弹窗的打开和关闭
const showDrawer = defineModel<boolean>({ default: false });

// 定义拥有步骤条的菜单页面
const STEP_PAGES = [
  "businessConfigSetting",
  "joinOperatorSetting",
  "exceptionHandlerSetting",
];

import { useflowChartStore } from "@/store/modules/flowChart";
const flowChartStore = useflowChartStore();

// 定义当前步骤进度
const stepActive = ref(0);

const isStepShow = computed(() => {
  if (props.selectMenuItem && STEP_PAGES.includes(props.selectMenuItem)) {
    stepActive.value = flowChartStore.pageStep;
    return true;
  }
  return false;
});

// 定义需要确认的步骤条菜单页面
const STEP_PAGES_CONFIME = ["joinOperatorSetting", "exceptionHandlerSetting"];

const isStepShowConfime = computed(() => {
  return (
    (!isStepShow.value ||
      stepActive.value == 2 ||
      (props.selectMenuItem &&
        STEP_PAGES_CONFIME.includes(props.selectMenuItem))) &&
    props.showConfirm
  );
});

// 定义步骤条页面有几个
const STEP_PAGES_NUM: Record<string, number> = {
  businessConfigSetting: 3,
  joinOperatorSetting: 3,
  exceptionHandlerSetting: 2,
};

// 判断当前页面是否可以继续下一步
const justPageNum = computed(() => {
  if (props.selectMenuItem && STEP_PAGES.includes(props.selectMenuItem)) {
    return stepActive.value < STEP_PAGES_NUM[props.selectMenuItem] - 1;
  }
  return false;
});

function handleMenuSelect(key: string) {
  emit("updateSelect", key);
}
</script>

<style lang="scss" scoped>
.drawer-content {
  display: flex;
  height: 100%;
  background: #ffffff;
  padding: 24px 16px;
  .right-content {
    padding: 8px 16px 8px 32px;
    width: 100%;
    overflow: auto;
    //min-width: calc(100% - 200px);
  }
  .tool-span {
    margin-left: 8px;
    max-width: 100px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
<style lang="scss">
.drawer-menu-class {
  .el-drawer__header {
    height: 56px;
    line-height: 56px;
    padding: 0 16px;
    margin: 0px;
    span {
      font-weight: 500;
      font-size: 16px;
      color: #1d2129;
      line-height: 22px;
    }
  }
  .el-drawer__body {
    background: #f2f3f5;
    padding: 8px;
    .el-menu-item {
      height: 40px;
      line-height: 40px;
      margin-bottom: 6px;
    }
    .el-menu-item.is-active {
      border-right: 2px solid var(--el-color-primary);
      background-color: var(--el-menu-hover-bg-color);
    }
  }
  .el-drawer__footer {
    padding: 12px 16px;
  }
}
</style>
