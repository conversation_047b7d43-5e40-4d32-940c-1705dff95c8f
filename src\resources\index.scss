@import "./style/css-vars-light.scss";

html,
body,
#app {
  margin: 0;
  height: 100%;
}

// 页面表单通用设置
.page {
  width: 100%;
  height: 100%;
  .form-style {
    .el-form-item {
      margin-bottom: 24px;
    }
    .el-form-item.is-success .el-input__validateIcon {
      color: #4c98fa;
    }
    .el-form-item--label-top .el-form-item__label {
      margin-bottom: 4px;
    }
    .form-item-tips {
      font-weight: 400;
      font-size: 12px;
      color: #86909c;
      width: 100%;
      clear: both;
      line-height: 1.5;
      margin-top: 4px;
      min-height: 19px;
      transition: color 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
    }
    .el-form-item.is-error .form-item-tips {
      color: #f5222d;
    }
  }
  // 分段组件样式处理
  .footer_style {
    width: 100%;
    display: flex;
    justify-content: flex-end;
    line-height: 32px;
    margin-top: 8px;
  }
  .el-button + .el-button {
    margin-left: 8px;
  }
}

// 专门针对于页面表单中的表格样式处理
.page-table {
  display: flex;
  flex-direction: column;
  .btn-refresh {
    margin-bottom: 16px;
    width: fit-content;
  }
  .form-table-style {
    flex: 0 1 auto;
    max-height: 100%;
    .form-table-height {
      display: flex;
      height: 100%;
      min-height: 120px;
      margin-bottom: 0 !important;
    }
  }
}

.fullheight {
  height: 100% !important;
}

// 单独对t-input做样式调整
.t-input-number .t-input__wrap {
  .t-input {
    box-shadow: none !important;
    .t-input__inner {
      color: #606266 !important;
    }
  }

  .t-input:hover {
    border-color: var(--el-color-primary);
  }

  .t-input:focus {
    border-color: var(--el-color-primary);
  }

  .t-input--focused {
    border-color: var(--el-color-primary);
  }
}

// tag各个类型的定义
.el-tag.tag-status-success {
  background-color: rgba(0, 180, 42, 0.1);
  color: rgba(0, 180, 42, 1);
  border-color: rgba(0, 180, 42, 0.1);
}

.el-tag.tag-status-fail {
  background-color: rgba(255, 2, 2, 0.1);
  color: rgba(255, 2, 2, 1);
  border-color: rgba(255, 2, 2, 0.1);
}

.el-tag.tag-status-warning {
  background-color: rgba(255, 125, 0, 0.1);
  color: rgba(255, 125, 0, 1);
  border-color: rgba(255, 125, 0, 0.1);
}

.el-tag.tag-status-0 {
  background-color: var(--el-status-bg-color-0);
  color: var(--el-status-text-color-0);
  border-color: var(--el-status-bg-color-0);
}

.el-tag.tag-status-1 {
  background-color: var(--el-status-bg-color-1);
  color: var(--el-status-text-color-1);
  border-color: var(--el-status-bg-color-1);
}

.el-tag.tag-status-2 {
  background-color: var(--el-status-bg-color-2);
  color: var(--el-status-text-color-2);
  border-color: var(--el-status-bg-color-2);
}

.el-tag.tag-status-3 {
  background-color: var(--el-status-bg-color-3);
  color: var(--el-status-text-color-3);
  border-color: var(--el-status-bg-color-3);
}

.el-tag.tag-status-4 {
  background-color: var(--el-status-bg-color-4);
  color: var(--el-status-text-color-4);
  border-color: var(--el-status-bg-color-4);
}

.el-tag.tag-status-5 {
  background-color: var(--el-status-bg-color-5);
  color: var(--el-status-text-color-5);
  border-color: var(--el-status-bg-color-5);
}

.t-tag-input.t-input__wrap {
  .t-input {
    box-shadow: none !important;
    .t-input__inner {
      color: #606266 !important;
    }
  }

  .t-input:hover {
    border-color: var(--el-color-primary);
  }

  .t-input:focus {
    border-color: var(--el-color-primary);
  }

  .t-input--focused {
    border-color: var(--el-color-primary);
  }
  .t-tag--default {
    background-color: var(--el-color-info-light-9);
    //border-color: var(--el-color-info-light-8);
    color: var(--el-color-info);
  }
}
