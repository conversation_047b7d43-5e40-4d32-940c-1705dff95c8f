<template>
  <div class="page">
    <el-form
      class="form-style"
      ref="ruleFormRef"
      label-position="top"
      require-asterisk-position="right"
      label-width="auto"
      :model="redisOutputData"
      :rules="redisOutputDataRules"
      :show-message="false"
    >
      <el-form-item label="节点名称" prop="name">
        <el-input v-model="redisOutputData.name" maxlength="20"></el-input>
        <div class="form-item-tips">指定任何有意义且能描述本节点的说明</div>
      </el-form-item>
      <el-form-item label="节点ID" prop="id">
        <el-input v-model="redisOutputData.id" disabled></el-input>
        <div class="form-item-tips">节点ID不能重复</div>
      </el-form-item>
      <el-form-item label="表名" prop="redisTableName">
        <el-input
          v-model="redisOutputData.redisTableName"
          maxlength="50"
        ></el-input>
        <div class="form-item-tips">
          指定要写入数据的表名（注：只能输入字母数字下划线，且首字符只能是字母并最大不超过50个字符）
        </div>
      </el-form-item>
      <el-form-item label="索引字段" prop="rediskey">
        <div style="display: flex; width: 100%">
          <el-select
            v-model="redisOutputData.rediskey"
            multiple
            collapse-tags
            collapse-tags-tooltip
            :max-collapse-tags="2"
          >
            <el-option
              v-for="item in rediskeyOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <el-button
            style="margin-left: 4px"
            :icon="RefreshRight"
            @click="loadField"
          >
            载入字段
          </el-button>
        </div>
        <div class="form-item-tips">指定要写入数据的索引字段</div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { RefreshRight } from "@element-plus/icons-vue";

// 引入通用的校验规则
import {
  getFormRules,
  getConnectData,
  getNodeFrom,
  getResetField,
  getCommonEnumField,
} from "@/utils/common";

const props = defineProps<{
  showDrawer: boolean;
  keyMenuItem: string;
}>();

// 获取爷爷的响应式数据
const inputData = inject("inputData") as Ref<AllOutputParam>;

// 数据监听  -- 通过监听去对弹窗数据进行重置
watch(
  () => props.showDrawer,
  async (val) => {
    if (!val) return;
    // 使用async和await来处理nextTick，减少代码层级
    await nextTick();
    redisOutputData.value = cloneDeep(inputData.value);
    redisOutputData.value.from ??= [];
    redisOutputData.value.filterTable ??= [];
    refreshOptions();
    // 延迟进行表单校验的清空操作才会生效,处理下拉框的change导致进入弹窗的校验触发
    setTimeout(() => {
      clearValidate();
    }, 10);
  },
  {
    immediate: true,
  }
);

const redisOutputData = ref<AllOutputParam>({
  id: "",
  name: "",
});

import { useflowChartStore } from "@/store/modules/flowChart";
const flowChartStore = useflowChartStore();

// 定义页面名称
const componentPageName = "redisSinkSetting";

// 用于监听菜单是否进行切换到指定页面
watch(
  () => props.keyMenuItem,
  (newVal, oldVal) => {
    if (oldVal === componentPageName) {
      flowChartStore.updateTempData(
        inputData.value.nodeType,
        redisOutputData.value
      );
    }
  }
);

// 表单校验规则
import type { FormInstance } from "element-plus";

const ruleFormRef = ref<FormInstance>();

const redisOutputDataRules = {
  id: [
    {
      required: true,
      trigger: ["blur", "change"],
    },
  ],
  redisTableName: [
    {
      required: true,
      trigger: ["blur", "change"],
    },
    {
      pattern: /^[a-zA-Z][a-zA-Z0-9_]*$/,
      message: "只能输入字母数字下划线，且首字符只能是字母",
      trigger: ["blur", "change"],
    },
  ],
  rediskey: [
    {
      required: true,
      trigger: ["blur", "change"],
    },
  ],
};

import { useVueFlow } from "@vue-flow/core";

const { getIncomers } = useVueFlow();

// 数据所在节点
const locatedNodeOptions = ref<ComputerOptionsParam[]>([]);

// 回显判断是否重新获取字段信息
function refreshOptions() {
  getlocatedData();
  if (!isEmpty(redisOutputData.value.rediskey)) {
    loadField();
  } else {
    rediskeyOptions.value.length = 0;
  }
}

// 获取与之连接数据连接
function getlocatedData() {
  locatedNodeOptions.value.length = 0;
  // 获取当前节点的所有入边
  const incomers = getIncomers(inputData.value.id);
  locatedNodeOptions.value = getResetField(incomers);
}

// 索引字段
const rediskeyOptions = ref<NodeOptionsParam[]>([]);

async function loadField() {
  rediskeyOptions.value.length = 0;
  // 调用方法获取字段数据
  const resData = (await getConnectData(
    locatedNodeOptions.value
  )) as ConnectDataParam;
  const { getTableData, filterData } = resData;
  redisOutputData.value.dataStream = filterData?.dataStream;
  if (isEmpty(getTableData)) return;
  redisOutputData.value.filterTable = cloneDeep(getTableData);
  rediskeyOptions.value = getCommonEnumField(getTableData);
}

// 进行保存校验
async function getNewData() {
  redisOutputData.value.from = getNodeFrom(locatedNodeOptions.value);
  const childRuleBool = await getFormRules(
    ruleFormRef.value,
    inputData.value.nodeType,
    redisOutputData.value,
    "基本配置"
  );
  return childRuleBool;
}

// 关闭弹窗清楚校验
function clearValidate() {
  ruleFormRef.value?.clearValidate();
}

// 放开方法给父组件使用
defineExpose({
  getNewData,
});
</script>

<style lang="scss" scoped>
.page {
  height: 100%;
}
</style>
