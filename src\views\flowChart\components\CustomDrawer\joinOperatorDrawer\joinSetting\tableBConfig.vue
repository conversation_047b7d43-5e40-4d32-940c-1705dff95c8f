<template>
  <div class="page page-table">
    <el-button class="btn-refresh" @click="refeshTableB">
      <svg-icon name="reImport" style="margin-right: 8px"></svg-icon>
      重新导入字段
    </el-button>
    <div class="form-style form-table-style">
      <el-form
        class="fullheight"
        ref="ruleFormRef"
        :model="joinOperatorData"
        :rules="joinOperatorDataRules"
        :show-message="false"
        label-width="auto"
      >
        <el-form-item prop="tableData" class="form-table-height">
          <el-table
            :data="joinOperatorData.tableB"
            scrollbar-always-on
            style="height: calc(100% - 23px)"
          >
            <template v-for="item in fieldTableHeader">
              <el-table-column
                v-bind="item"
                show-overflow-tooltip
              ></el-table-column>
            </template>
            <el-table-column label="操作" width="100" align="center">
              <template #default="scope">
                <el-button
                  link
                  type="danger"
                  @click="clickDelete(scope.$index)"
                >
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="form-item-tips">
            注意:B表合并后的数据将以此字段配置为准，没有配置在本字段列表中的字段将被删除
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
// 获取父组件的ref
const props = defineProps<{
  showDrawer: boolean;
  keyMenuItem: string;
  stepActive: number;
  componentPageName: string;
}>();

// 获取爷爷的响应式数据
const inputData = inject("inputData") as Ref<EditDrawerParam>;

// 数据监听  -- 通过监听去对弹窗数据进行重置
watch(
  () => props.showDrawer,
  async (val) => {
    if (!val) return;
    // 使用async和await来处理nextTick，减少代码层级
    await nextTick();
    joinOperatorData.value.id = inputData.value.id;
    if (!isEmpty(inputData.value.tableB)) {
      joinOperatorData.value.tableB = cloneDeep(inputData.value.tableB);
    }
  },
  {
    immediate: true,
  }
);

// 用于监听菜单是否进行切换到指定页面
watch(
  () => props.keyMenuItem,
  (newVal, oldVal) => {
    if (oldVal === props.componentPageName && props.stepActive === 2) {
      flowChartStore.updateTempData(
        inputData.value.nodeType,
        joinOperatorData.value
      );
    }
  }
);

const joinOperatorData = ref<EditDrawerParam>({
  id: "",
});

import { fieldTableHeader } from "@/utils/globalField";

import { useflowChartStore } from "@/store/modules/flowChart";
const flowChartStore = useflowChartStore();

// 监听步骤发现改变获取最新的数据
watch(
  () => props.stepActive,
  (val) => {
    // 第一次进入步骤获取表格数据
    if (val === 2 && !joinOperatorData.value.tableB?.length) {
      joinOperatorData.value.tableB = flowChartStore.tempComputerData?.tableB;
    }
  }
);

function refeshTableB() {
  const id = flowChartStore.tempComputerData?.streamBM;
  queryFieldTableData(id);
}

import { useVueFlow } from "@vue-flow/core";
import type { Node } from "@vue-flow/core";

const { getNodes } = useVueFlow();

const currentNodes = ref<Node[]>([]);

// 监听节点变化
watch(
  getNodes,
  (nodes) => {
    currentNodes.value = nodes;
  },
  {
    immediate: true,
    deep: true,
  }
);

// 获取表格字段数据
async function queryFieldTableData(id?: string) {
  // 清空数据
  joinOperatorData.value.tableB = [];
  const data = currentNodes.value?.find((item) => item.id === id);
  if (!data) return;
  const locatedNodeOptions = {
    value: data.id,
    label: data.data.name + "（" + data.id + "）",
    key: data.data.type,
    dataStream: data.data.dataStream,
    nodeType: data.data.nodeType,
  };
  // 调用方法获取字段数据
  const resData = (await getConnectData([
    locatedNodeOptions,
  ])) as ConnectDataParam;
  const { getTableData } = resData;
  if (isEmpty(getTableData)) return;
  joinOperatorData.value.tableB = cloneDeep(getTableData);
}

import type { FormInstance } from "element-plus";

const ruleFormRef = ref<FormInstance>();

const joinOperatorDataRules = {
  tableB: [
    {
      required: false,
      trigger: ["blur", "change"],
    },
  ],
};

function clickDelete(index: number) {
  joinOperatorData.value.tableB?.splice(index, 1);
}

// 引入通用的校验规则
import { getFormRules, getConnectData } from "@/utils/common";

async function getNewData() {
  const childRuleBool = await getFormRules(
    ruleFormRef.value,
    inputData.value.nodeType,
    joinOperatorData.value,
    "B表字段配置"
  );
  return childRuleBool;
}

// 判断输入源是否修改导致字段进行修改
function checkFieldUpdate() {
  if (isEmpty(inputData.value.tableB)) return false;
  return !isEqual(joinOperatorData.value.tableB, inputData.value.tableB);
}

// 放开方法给父组件使用
defineExpose({
  getNewData,
  checkFieldUpdate,
});
</script>

<style lang="scss" scoped>
.page {
  height: calc(100% - 80px);
  .form-table-style {
    max-height: calc(100% - 50px);
  }
}
</style>
