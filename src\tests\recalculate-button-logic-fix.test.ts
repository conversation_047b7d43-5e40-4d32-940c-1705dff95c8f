import { describe, it, expect, vi } from 'vitest'

describe('重算按钮逻辑修正验证', () => {
  // 模拟ProcessTableParam类型
  interface ProcessTableParam {
    id: string | number
    status?: string // 流程状态
    recalculateStatus?: string // 重算状态
    isRunning?: boolean
    isRecalculating?: boolean
    recalculateRunningTime?: string
    updateTime?: string
  }

  // 修正后的重算状态判断函数
  function isRecalculatingSubmittingStatus(row: ProcessTableParam): boolean {
    // 当重算状态为RUNNING时，显示"正在运行中"状态（loading状态）
    return row.recalculateStatus === 'RUNNING';
  }

  // 修正后的重算开关变化处理函数
  function recalculateSwitchChange(row: ProcessTableParam, emit: Function) {
    const curSwitch = row.isRecalculating;
    if (curSwitch) {
      // 开启重算 - 这种情况不应该发生，因为beforeRecalculateChange会阻止
      emit("recalculateClick", row);
    } else {
      // 关闭重算
      emit("cancelRecalculateTask", row);
    }
  }

  // 修正后的重算运行时间获取函数
  function getRecalculateRunning(row: ProcessTableParam): string {
    const RECALCULATE_RUNNING_STATUS = ["RUNNING"];
    
    if (row.recalculateStatus && RECALCULATE_RUNNING_STATUS.includes(row.recalculateStatus)) {
      const timeValue = row.recalculateRunningTime || row.updateTime;
      return timeValue || "--";
    }
    
    return "--";
  }

  // 修正后的运行状态设置函数
  function addRunningColumn(tableData: ProcessTableParam[]) {
    const RUNNING_STATUS = ["RUNNING", "SUBMITTED"];
    const RECALCULATING_STATUS = ["RUNNING"];

    tableData.forEach((item: ProcessTableParam) => {
      if (item.status) {
        item.isRunning = RUNNING_STATUS.includes(item.status);
      }
      // 重算状态基于recalculateStatus字段判断
      if (item.recalculateStatus) {
        item.isRecalculating = RECALCULATING_STATUS.includes(item.recalculateStatus);
      } else {
        item.isRecalculating = false;
      }
    });
  }

  describe('重算状态判断', () => {
    it('当重算状态为RUNNING时，应该返回true（显示正在运行中）', () => {
      const row: ProcessTableParam = { 
        id: 1, 
        recalculateStatus: 'RUNNING' 
      };
      expect(isRecalculatingSubmittingStatus(row)).toBe(true);
    });

    it('当重算状态不是RUNNING时，应该返回false（显示停止）', () => {
      const testCases = [
        { recalculateStatus: 'CANCELED' },
        { recalculateStatus: 'FAILED' },
        { recalculateStatus: 'SUBMITTED' },
        { recalculateStatus: undefined },
        { recalculateStatus: '' },
      ];

      testCases.forEach(({ recalculateStatus }) => {
        const row: ProcessTableParam = { id: 1, recalculateStatus };
        expect(isRecalculatingSubmittingStatus(row)).toBe(false);
      });
    });
  });

  describe('按钮文本显示', () => {
    it('当重算状态为RUNNING时，应该显示"正在运行中/关闭任务"', () => {
      const row: ProcessTableParam = { 
        id: 1, 
        recalculateStatus: 'RUNNING' 
      };
      
      const activeText = isRecalculatingSubmittingStatus(row) ? '正在运行中' : '重算 ';
      const inactiveText = isRecalculatingSubmittingStatus(row) ? '关闭任务' : '停止 ';
      
      expect(activeText).toBe('正在运行中');
      expect(inactiveText).toBe('关闭任务');
    });

    it('当重算状态不是RUNNING时，应该显示"重算/停止"', () => {
      const row: ProcessTableParam = { 
        id: 1, 
        recalculateStatus: 'CANCELED' 
      };
      
      const activeText = isRecalculatingSubmittingStatus(row) ? '正在运行中' : '重算 ';
      const inactiveText = isRecalculatingSubmittingStatus(row) ? '关闭任务' : '停止 ';
      
      expect(activeText).toBe('重算 ');
      expect(inactiveText).toBe('停止 ');
    });
  });

  describe('开关变化处理', () => {
    it('当isRecalculating为true时，应该触发recalculateClick事件', () => {
      const row: ProcessTableParam = { 
        id: 1, 
        isRecalculating: true 
      };
      
      const mockEmit = vi.fn();
      recalculateSwitchChange(row, mockEmit);
      
      expect(mockEmit).toHaveBeenCalledWith("recalculateClick", row);
    });

    it('当isRecalculating为false时，应该触发cancelRecalculateTask事件', () => {
      const row: ProcessTableParam = { 
        id: 1, 
        isRecalculating: false 
      };
      
      const mockEmit = vi.fn();
      recalculateSwitchChange(row, mockEmit);
      
      expect(mockEmit).toHaveBeenCalledWith("cancelRecalculateTask", row);
    });
  });

  describe('重算运行时间显示', () => {
    it('当重算状态为RUNNING时，应该显示时间', () => {
      const row: ProcessTableParam = { 
        id: 1, 
        recalculateStatus: 'RUNNING',
        recalculateRunningTime: '2023-12-01 10:00:00'
      };
      
      expect(getRecalculateRunning(row)).toBe('2023-12-01 10:00:00');
    });

    it('当重算状态为RUNNING但没有专门时间时，应该使用updateTime', () => {
      const row: ProcessTableParam = { 
        id: 1, 
        recalculateStatus: 'RUNNING',
        updateTime: '2023-12-01 09:00:00'
      };
      
      expect(getRecalculateRunning(row)).toBe('2023-12-01 09:00:00');
    });

    it('当重算状态不是RUNNING时，应该显示"--"', () => {
      const testCases = [
        { recalculateStatus: 'CANCELED' },
        { recalculateStatus: 'FAILED' },
        { recalculateStatus: undefined },
      ];

      testCases.forEach(({ recalculateStatus }) => {
        const row: ProcessTableParam = { 
          id: 1, 
          recalculateStatus,
          recalculateRunningTime: '2023-12-01 10:00:00'
        };
        expect(getRecalculateRunning(row)).toBe('--');
      });
    });
  });

  describe('运行状态设置', () => {
    it('应该正确设置isRecalculating状态', () => {
      const tableData: ProcessTableParam[] = [
        { id: 1, status: 'RUNNING', recalculateStatus: 'RUNNING' },
        { id: 2, status: 'CANCELED', recalculateStatus: 'CANCELED' },
        { id: 3, status: 'SUBMITTED', recalculateStatus: undefined },
        { id: 4, status: 'FAILED', recalculateStatus: 'RUNNING' },
      ];

      addRunningColumn(tableData);

      expect(tableData[0].isRecalculating).toBe(true); // recalculateStatus: RUNNING
      expect(tableData[1].isRecalculating).toBe(false); // recalculateStatus: CANCELED
      expect(tableData[2].isRecalculating).toBe(false); // recalculateStatus: undefined
      expect(tableData[3].isRecalculating).toBe(true); // recalculateStatus: RUNNING
    });

    it('应该正确设置isRunning状态', () => {
      const tableData: ProcessTableParam[] = [
        { id: 1, status: 'RUNNING' },
        { id: 2, status: 'SUBMITTED' },
        { id: 3, status: 'CANCELED' },
        { id: 4, status: 'FAILED' },
      ];

      addRunningColumn(tableData);

      expect(tableData[0].isRunning).toBe(true); // status: RUNNING
      expect(tableData[1].isRunning).toBe(true); // status: SUBMITTED
      expect(tableData[2].isRunning).toBe(false); // status: CANCELED
      expect(tableData[3].isRunning).toBe(false); // status: FAILED
    });
  });

  describe('综合场景测试', () => {
    it('重算状态为RUNNING的完整流程', () => {
      const row: ProcessTableParam = { 
        id: 1, 
        status: 'CANCELED',
        recalculateStatus: 'RUNNING',
        recalculateRunningTime: '2023-12-01 10:00:00'
      };

      // 设置运行状态
      addRunningColumn([row]);

      // 验证状态设置
      expect(row.isRecalculating).toBe(true);
      expect(row.isRunning).toBe(false);

      // 验证按钮文本
      expect(isRecalculatingSubmittingStatus(row)).toBe(true);
      
      // 验证运行时间显示
      expect(getRecalculateRunning(row)).toBe('2023-12-01 10:00:00');
    });

    it('重算状态为非RUNNING的完整流程', () => {
      const row: ProcessTableParam = { 
        id: 1, 
        status: 'RUNNING',
        recalculateStatus: 'CANCELED',
        recalculateRunningTime: '2023-12-01 10:00:00'
      };

      // 设置运行状态
      addRunningColumn([row]);

      // 验证状态设置
      expect(row.isRecalculating).toBe(false);
      expect(row.isRunning).toBe(true);

      // 验证按钮文本
      expect(isRecalculatingSubmittingStatus(row)).toBe(false);
      
      // 验证运行时间显示
      expect(getRecalculateRunning(row)).toBe('--');
    });
  });
});
