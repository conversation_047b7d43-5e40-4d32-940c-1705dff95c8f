<template>
  <aside class="dnd_aside">
    <el-collapse v-model="activeName">
      <template v-for="node in nodeTypeLabelMapTest" :key="node.type">
        <el-collapse-item :title="node.title" :name="node.type">
          <div class="nodes">
            <template v-for="item in node.children" :key="item.value">
              <div
                class="vue-flow__node-default"
                :class="{
                  'disabled-style': !isAllNodeDisabled,
                }"
                :draggable="!!isAllNodeDisabled"
                @dragstart="
                  onDragStart(
                    $event,
                    item.key,
                    item.value,
                    item.label,
                    id,
                    item.dataStream
                  )
                "
              >
                <svg-icon
                  :name="item.icon || ''"
                  style="margin: 0px 8px 0px 16px"
                ></svg-icon>
                {{ item.label }}
              </div>
            </template>
          </div>
        </el-collapse-item>
      </template>
    </el-collapse>
  </aside>
</template>

<script setup lang="ts">
const props = defineProps<{
  id: string;
}>();

import { nodeTypeLabelMapTest } from "@/utils/globalField";

// 引入节点拖拽功能的使用
import useDragAndDrop from "@/utils/useDrag";

const { onDragStart } = useDragAndDrop();

import { useVueFlow } from "@vue-flow/core";

const { getNodes } = useVueFlow();

// 流程图最大节点数量
import { maxNodeNum } from "@/utils/globalField";

// 监听节点变化，体节点的数量限制
const isAllNodeDisabled = computed(() => {
  return getNodes.value.length < maxNodeNum;
});

// 初始进入打开输入算子的折叠
const activeName = ref(["source"]);
</script>

<style scoped lang="scss">
.dnd_aside {
  color: #666666;
  font-weight: 700;
  border: 1px solid #e5e6eb;
  padding: 8px 24px 24px 24px;
  background: #ffffff;
  box-sizing: border-box;
  min-width: 256px;
  overflow-y: auto;
  max-height: 90vh;
  :deep(.el-collapse-item__header) {
    font-size: 14px;
    font-weight: 400;
  }
  :deep(.el-collapse) {
    border-top: none; /* 去除上边框 */
  }
  :deep(.el-collapse-item__content) {
    padding-bottom: 8px;
  }
}

.dnd_aside .nodes > * {
  margin-bottom: 16px;
  cursor: grab;
  font-weight: 500;
  box-sizing: border-box;
  width: 206px;
  height: 36px;
  line-height: 36px;
  padding: 0px;
  border: 1px solid #e5e6eb;
  border-radius: 4px;
  font-weight: 400;
  font-size: 14px;
  color: #1d2129;
  text-align: left;
}
.dnd_aside .nodes > *:hover {
  background: rgba(22, 93, 255, 0.1);
}

.disabled-style {
  cursor: not-allowed !important;
  opacity: 0.5 !important;
}

.disabled-style:hover {
  background: none !important;
}
</style>
