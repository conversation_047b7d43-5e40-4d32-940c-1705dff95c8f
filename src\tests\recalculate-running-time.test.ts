import { describe, it, expect, vi } from 'vitest'

describe('Recalculate Running Time Display Logic', () => {
  // 模拟common.formatterTime函数
  const mockFormatterTime = vi.fn(() => (row: any, column: any, cellValue: any, index: any) => {
    if (!cellValue) return '--';
    return `格式化时间: ${cellValue}`;
  });

  // 模拟common对象
  const common = {
    formatterTime: mockFormatterTime
  };

  // 模拟getRecalculateRunning函数
  function getRecalculateRunning(row: any) {
    // 当流程任务是重算运行提交成功时，显示时间；其他状态显示 "--"
    const RECALCULATE_RUNNING_STATUS = ["RECALCULATING"];
    
    if (row.status && RECALCULATE_RUNNING_STATUS.includes(row.status)) {
      // 如果有专门的重算运行时间字段，使用它；否则使用更新时间
      const timeValue = row.recalculateRunningTime || row.updateTime;
      return timeValue ? common.formatterTime()(row, null, timeValue, null) : "--";
    }
    
    return "--";
  }

  // 测试数据
  const testCases = [
    {
      name: 'RECALCULATING状态且有recalculateRunningTime',
      data: { 
        id: 1, 
        status: 'RECALCULATING', 
        recalculateRunningTime: '2024-01-01 10:00:00',
        updateTime: '2024-01-01 09:00:00'
      },
      expected: '格式化时间: 2024-01-01 10:00:00'
    },
    {
      name: 'RECALCULATING状态但无recalculateRunningTime，有updateTime',
      data: { 
        id: 2, 
        status: 'RECALCULATING', 
        updateTime: '2024-01-01 09:00:00'
      },
      expected: '格式化时间: 2024-01-01 09:00:00'
    },
    {
      name: 'RECALCULATING状态但无任何时间',
      data: { 
        id: 3, 
        status: 'RECALCULATING'
      },
      expected: '--'
    },
    {
      name: 'RUNNING状态',
      data: { 
        id: 4, 
        status: 'RUNNING', 
        recalculateRunningTime: '2024-01-01 10:00:00'
      },
      expected: '--'
    },
    {
      name: 'SUBMITTED状态',
      data: { 
        id: 5, 
        status: 'SUBMITTED', 
        recalculateRunningTime: '2024-01-01 10:00:00'
      },
      expected: '--'
    },
    {
      name: 'FAILED状态',
      data: { 
        id: 6, 
        status: 'FAILED', 
        recalculateRunningTime: '2024-01-01 10:00:00'
      },
      expected: '--'
    },
    {
      name: 'CANCELED状态',
      data: { 
        id: 7, 
        status: 'CANCELED', 
        recalculateRunningTime: '2024-01-01 10:00:00'
      },
      expected: '--'
    },
    {
      name: 'CANCELLING状态',
      data: { 
        id: 8, 
        status: 'CANCELLING', 
        recalculateRunningTime: '2024-01-01 10:00:00'
      },
      expected: '--'
    },
    {
      name: '无状态',
      data: { 
        id: 9, 
        recalculateRunningTime: '2024-01-01 10:00:00'
      },
      expected: '--'
    }
  ];

  testCases.forEach(({ name, data, expected }) => {
    it(`should handle ${name}`, () => {
      const result = getRecalculateRunning(data);
      expect(result).toBe(expected);
    });
  });

  it('should prioritize recalculateRunningTime over updateTime when both exist', () => {
    const data = {
      id: 1,
      status: 'RECALCULATING',
      recalculateRunningTime: '2024-01-01 10:00:00',
      updateTime: '2024-01-01 09:00:00'
    };

    const result = getRecalculateRunning(data);
    expect(result).toBe('格式化时间: 2024-01-01 10:00:00');
  });

  it('should only show time for RECALCULATING status', () => {
    const statuses = ['RECALCULATING', 'RUNNING', 'SUBMITTED', 'FAILED', 'CANCELED', 'CANCELLING'];
    const timeValue = '2024-01-01 10:00:00';

    statuses.forEach(status => {
      const data = { id: 1, status, recalculateRunningTime: timeValue };
      const result = getRecalculateRunning(data);
      
      if (status === 'RECALCULATING') {
        expect(result).toBe('格式化时间: 2024-01-01 10:00:00');
      } else {
        expect(result).toBe('--');
      }
    });
  });

  it('should handle edge cases', () => {
    // 空对象
    expect(getRecalculateRunning({})).toBe('--');
    
    // null状态
    expect(getRecalculateRunning({ status: null, recalculateRunningTime: '2024-01-01 10:00:00' })).toBe('--');
    
    // 空字符串状态
    expect(getRecalculateRunning({ status: '', recalculateRunningTime: '2024-01-01 10:00:00' })).toBe('--');
    
    // undefined状态
    expect(getRecalculateRunning({ status: undefined, recalculateRunningTime: '2024-01-01 10:00:00' })).toBe('--');
  });

  it('should be independent from real time running logic', () => {
    // 验证重算运行时间和实时运行时间是独立的
    const data = {
      id: 1,
      status: 'RECALCULATING',
      runningTime: '2024-01-01 08:00:00',
      recalculateRunningTime: '2024-01-01 10:00:00',
      updateTime: '2024-01-01 09:00:00'
    };

    // 重算运行时间应该使用recalculateRunningTime
    const result = getRecalculateRunning(data);
    expect(result).toBe('格式化时间: 2024-01-01 10:00:00');
  });
})
