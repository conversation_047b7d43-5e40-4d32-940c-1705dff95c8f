# RECALCULATING状态处理测试指南

## 需求分析
当任务的流程状态为`RECALCULATING`时，当前流程的运行状态修改为`running`。

## 实现方案

### 1. 状态映射逻辑
- **流程状态**: `RECALCULATING` 
- **运行状态**: `true` (显示为运行中)
- **UI显示**: 绿色标签，显示"RECALCULATING"

### 2. 实现的功能点

#### ✅ 状态颜色配置
- 在`STATUS_COLOR`中添加`RECALCULATING: "success"`
- 重算状态显示为绿色标签（与RUNNING状态相同颜色）

#### ✅ 运行状态映射
- 在`RUNNING_STATUS`数组中添加`"RECALCULATING"`
- 当状态为`RECALCULATING`时，`isRunning`自动设置为`true`

#### ✅ 按钮禁用逻辑
- 在`DISABLE_STATUS_DATA`中添加`"RECALCULATING"`
- 重算期间禁用编辑、删除、流程设计等操作
- 在`DISABLED_DATA`中添加`"RECALCULATING"`
- 重算期间禁用运行/停止切换

#### ✅ 即时状态更新
- 重算启动成功后立即更新当前行状态
- 避免等待接口刷新的延迟

## 测试场景

### 场景1: 重算启动时的状态变化
1. 点击重算按钮
2. 填写时间信息并确认
3. **预期结果**:
   - 流程状态立即变为`RECALCULATING`
   - 运行状态开关变为开启状态
   - 状态标签显示为绿色

### 场景2: 重算期间的按钮状态
1. 当流程状态为`RECALCULATING`时
2. **预期结果**:
   - 编辑按钮被禁用
   - 删除按钮被禁用
   - 重算按钮被禁用
   - 流程设计按钮被禁用
   - 运行/停止开关被禁用

### 场景3: 表格和卡片布局一致性
1. 在表格布局中验证状态显示
2. 切换到卡片布局验证状态显示
3. **预期结果**:
   - 两种布局下状态显示一致
   - 按钮禁用逻辑一致

## 代码修改清单

### 表格布局 (tableLayout.vue)
```typescript
// 状态颜色
const STATUS_COLOR = {
  // ...
  RECALCULATING: "success", // 新增
  // ...
}

// 禁用状态
const DISABLE_STATUS_DATA = ["RUNNING", "CANCELLING", "SUBMITTED", "RECALCULATING"]; // 新增RECALCULATING
const DISABLED_DATA = ["CANCELLING", "SUBMITTED", "RECALCULATING"]; // 新增RECALCULATING
```

### 卡片布局 (cardLayout.vue)
```typescript
// 相同的状态配置
const STATUS_COLOR = { /* ... */ RECALCULATING: "success" };
const DISABLE_STATUS_DATA = [/* ... */ "RECALCULATING"];
const DISABLED_DATA = [/* ... */ "RECALCULATING"];
```

### 主页面 (index.vue)
```typescript
// 运行状态映射
const RUNNING_STATUS = ["RUNNING", "SUBMITTED", "RECALCULATING"]; // 新增RECALCULATING

// 重算确认后立即更新状态
async function handleRecalculateConfirm(data: any) {
  // 成功后立即设置状态
  if (currentRecalculateRow.value) {
    currentRecalculateRow.value.status = 'RECALCULATING';
    currentRecalculateRow.value.isRunning = true;
  }
}
```

## 验证方法

### 1. 手动测试
- 访问 http://localhost:9560/
- 找到一个可以重算的流程
- 点击重算按钮并确认
- 观察状态变化

### 2. 状态验证
- 检查流程状态标签是否显示为绿色的"RECALCULATING"
- 检查运行状态开关是否为开启状态
- 检查相关按钮是否正确禁用

### 3. 布局一致性验证
- 在表格和卡片布局间切换
- 确认状态显示一致

## ✅ 最新修复：显示文本问题

### 问题描述
之前实现中，`RECALCULATING`状态会显示"提交"而不是"运行"，不符合需求。

### 解决方案
将显示文本逻辑与禁用逻辑分离：

#### 修复前的问题
```typescript
// 问题：RECALCULATING既要禁用开关，又要显示"提交"
:active-text="disabledClick(scope.row) ? '提交' : '运行'"
```

#### 修复后的实现
```typescript
// 解决：分离显示逻辑和禁用逻辑
function isSubmittingStatus(row) {
  const SUBMITTING_STATUS = ["CANCELLING", "SUBMITTED"]; // 不包含RECALCULATING
  return SUBMITTING_STATUS.includes(row.status);
}

:active-text="isSubmittingStatus(scope.row) ? '提交' : '运行'"
```

### ✅ 修复验证
- **8个单元测试全部通过**
- `RECALCULATING`状态显示"运行"而不是"提交"
- 开关仍然正确禁用
- 不显示loading状态
- 表格和卡片布局保持一致

## 注意事项
- 重算状态的优先级与RUNNING状态相同
- 重算期间用户无法进行其他操作，但显示为"运行"状态
- 状态更新采用乐观更新策略，提升用户体验
- 后续需要后端支持返回RECALCULATING状态
