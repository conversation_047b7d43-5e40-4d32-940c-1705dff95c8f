import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";
import { resolve } from "path";
import AutoImport from "unplugin-auto-import/vite";
import Components from "unplugin-vue-components/vite";
import { ElementPlusResolver } from "unplugin-vue-components/resolvers";
// 引入svg打包插件
import { svgBuilder } from "./src/plugins/svgBuilder.ts";

export default defineConfig({
  plugins: [
    vue(),
    svgBuilder("./src/assets/icons/"),
    AutoImport({
      // 自动导入 Vue 相关函数，如：ref, reactive, toRef 等,以及lodash-es中常用方法
      imports: [
        "pinia",
        "vue",
        "vue-router",
        {
          "lodash-es": [
            // 按需导入
            "cloneDeep",
            "isEmpty",
            "isEqual",
          ],
        },
      ],
      resolvers: [ElementPlusResolver()],
      // 配置文件生成位置(false:关闭自动生成)
      dts: "src/types/auto-imports.d.ts",
    }),
    Components({
      resolvers: [ElementPlusResolver()],
      // 配置文件位置(false:关闭自动生成)
      dts: "src/types/components.d.ts",
    }),
  ],
  resolve: {
    alias: {
      // 配置别名指向src目录
      "@": resolve(__dirname, "./src"),
    },
    // 使用别名的文件后缀
    extensions: [".js", ".json", ".ts"],
  },
  // 服务设置和代理
  server: {
    host: "localhost",
    port: 9560,
    strictPort: false, // 允许端口被占用时自动选择其他端口
    proxy: {
      "/api": {
        target: "http://localhost:3000",
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ""),
      },
      "/flink-job": {
        // target: "http://************:18084", // 陈凯
        // target: "http://*************:18084", // 张蒲龙
        target: "http://************:18084", // 开发环境
        // target: "http://localhost:18084", // 本地调试环境
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/flink-job/, "/flink-job"),
      },
    },
  },
});
