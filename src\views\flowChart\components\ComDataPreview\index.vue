<template>
  <div class="page">
    <el-form
      class="form-style"
      ref="ruleFormRef"
      label-position="top"
      label-width="auto"
      :model="previewData"
      :rules="previewDataRules"
      :show-message="false"
    >
      <el-form-item label="调试数据来源" prop="debugDataSource" v-show="false">
        <el-select v-model="previewData.debugDataSource">
          <el-option
            v-for="item in sourceOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
    </el-form>
    <div class="table-style" v-if="!isEmpty">
      <el-table
        :data="tableData"
        :scrollbar-always-on="true"
        style="flex: 1"
        border
      >
        <template v-for="item in fieldTableHeader">
          <el-table-column
            v-bind="item"
            show-overflow-tooltip
          ></el-table-column>
        </template>
      </el-table>
      <el-pagination
        class="footer_style"
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :pager-count="5"
        :page-sizes="[10, 20, 50, 100]"
        size="small"
        :disabled="false"
        :background="true"
        layout="total,sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <el-empty v-else :image="emptyUrl" :image-size="400" />
  </div>
</template>

<script setup lang="ts">
// 引入通用的校验规则
import { getFormRules, nodeTypeFieldMap } from "@/utils/common";

const props = defineProps<{
  showDrawer?: boolean;
  keyMenuItem?: string;
  inputData: EditDrawerParam | AllInputParam | AllOutputParam;
  isShowDebug: boolean;
  componentPageName: string;
}>();

// 数据监听  -- 通过监听去对弹窗数据进行重置
watch(
  () => props.showDrawer,
  async (val) => {
    if (!val) return;
    // 使用async和await来处理nextTick，减少代码层级
    await nextTick();
    previewData.value.id = props.inputData.id;
    initPageCount();
    if (props.isShowDebug) {
      previewData.value.debugDataSource = props.inputData.debugDataSource;
    }
  },
  {
    immediate: true,
  }
);

function initPageCount() {
  currentPage.value = 1;
  pageSize.value = 20;
  pageParams.index = 0;
  pageParams.limit = 20;
}

const previewData = ref<EditDrawerParam | AllInputParam | AllOutputParam>({
  id: "",
});

// 定义流程列表表表头信息
const fieldTableHeader = ref();

const tableData = ref<FieldTableParam[]>([]);

// 用于监听菜单是否进行切换到指定页面
watch(
  () => props.keyMenuItem,
  (newVal, oldVal) => {
    if (newVal === props.componentPageName) {
      queryFieldTableData();
    }
  }
);

import { useflowChartStore } from "@/store/modules/flowChart";
const flowChartStore = useflowChartStore();

// 获取表格字段数据
async function queryFieldTableData() {
  tableData.value = [];
  const type = props.inputData.type;
  if (!type) return;
  const nodeType = props.inputData.nodeType;
  let storeData;
  switch (nodeType) {
    case "source":
      storeData = flowChartStore.tempInputData;
      break;
    case "sink":
      storeData = flowChartStore.tempOutputData;
      break;
    default:
      storeData = flowChartStore.tempComputerData;
      break;
  }

  const data = await nodeTypeFieldMap(type, storeData);

  getCurHeaderData(data);
}

import { common } from "@/utils/common";

function getCurHeaderData(data: FieldTableParam[]) {
  fieldTableHeader.value = [];
  const fieldTable: FilterTableParam[] = [
    {
      label: "序号",
      type: "index",
      width: "62",
    },
  ];
  data.forEach((item) => {
    const text = item.description + "(" + item.field + ")";
    fieldTable.push({
      prop: item.field as string,
      label: text,
      formatter:
        item.frontedType == "date"
          ? common.formatterTime()
          : common.formatEmptyText(),
      minWidth: getTextWidth(text) + 20 + "",
    });
  });
  if (fieldTable.length !== 1) {
    fieldTableHeader.value = fieldTable;
    getDebugData();
  }
}

// 获取当前字段的实际宽度
function getTextWidth(text: string) {
  const span = document.createElement("span");
  span.style.visibility = "hidden";
  span.style.position = "absolute";
  span.style.whiteSpace = "nowrap";
  span.innerText = text;
  // 将元素添加到文档中
  document.body.appendChild(span);
  // 获取元素宽度
  const width = span.offsetWidth;
  // 移除元素
  document.body.removeChild(span);
  return width;
}

import customApi from "@/api/custom";

// 获取路由参数
const routeState = window.history.state;

// 获取当前调试数据
async function getDebugData() {
  const params = {
    ...pageParams,
    id: props.inputData.id,
    jobName: routeState.jobName as string,
  };
  try {
    const res = await customApi.getDebugFile(params);
    tableData.value = res.data || [];
    total.value = res.total || 0;
  } catch (error) {
    tableData.value = [];
    total.value = 0;
    console.log(error);
  }
}

import type { FormInstance } from "element-plus";

const ruleFormRef = ref<FormInstance>();

const previewDataRules = {
  debugDataSource: [
    {
      required: false,
      trigger: ["blur", "change"],
    },
  ],
};

const sourceOptions = ref<OptionsParam[]>([]);

// 定义分段数据
const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);

// 定义分页入参, 该参数需要放在
const pageParams = reactive({
  index: 0,
  limit: 20,
});

function handleSizeChange(val: number) {
  currentPage.value = 1;
  pageSize.value = val;
  pageParams.index = 0;
  pageParams.limit = val;
  getDebugData();
}

function handleCurrentChange(val: number) {
  currentPage.value = val;
  pageParams.index = (val - 1) * pageSize.value;
  getDebugData();
}

async function getNewData() {
  const title = props.isShowDebug ? "调试预览" : "数据预览";
  const childRuleBool = await getFormRules(
    ruleFormRef.value,
    props.inputData.nodeType,
    previewData.value,
    title
  );
  return childRuleBool;
}

// 空图片展示
const emptyUrl = new URL("@/assets/images/empty.png", import.meta.url).href;

const isEmpty = computed(() => {
  return tableData.value.length === 0;
});

// 放开方法给父组件使用
defineExpose({
  getNewData,
});
</script>

<style lang="scss" scoped>
.page {
  height: 100%;
  display: flex;
  flex-direction: column;
  .table-style {
    display: flex;
    height: calc(100% - 12px);
    flex-direction: column;
  }
}
</style>
