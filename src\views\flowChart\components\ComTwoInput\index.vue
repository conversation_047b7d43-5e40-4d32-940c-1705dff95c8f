<template>
  <el-row>
    <el-col :span="11">
      <t-input-number v-model="range[0]" v-bind="$attrs" />
    </el-col>
    <el-col class="line" :span="2">-</el-col>
    <el-col :span="11">
      <t-input-number v-model="range[1]" v-bind="$attrs" />
    </el-col>
  </el-row>
</template>

<script setup lang="ts">
import { InputNumber as TInputNumber } from "tdesign-vue-next";

// 获取父组件v-model传递的值
const range = defineModel<(number | string)[]>({ default: [] });
</script>

<style lang="scss" scoped>
.line {
  text-align: center;
}
</style>
