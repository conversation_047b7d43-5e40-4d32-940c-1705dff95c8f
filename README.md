# 简介
TODO: 蓝云计算引擎项目，通过使用流程设计的方式快速搭建flink计算。

# 安装
TODO: 安装版本
1、node: v20.16.0
2、npm: 10.8.1

# 前端页面开发步骤（新增算子）

## 1. 创建算子目录结构
- 在 `flowChart/CustonDrawer/` 目录下新建 `exceptionHandlerDrawer` 文件夹
- 后续该算子的所有页面组件都放置在此目录中

## 2. 配置全局菜单
- 修改 `globalField` 文件：
  - 在流程设计页面左侧菜单添加新算子图标
  - 配置该算子弹窗的左侧菜单页面结构

## 3. 注册页面组件
- 在 `ComMenuDrawer` 页面中：
  - 将新开发的页面添加到步骤条页面管理系统中

## 4. 数据处理配置
- 开发完成后需在 `utils/common.ts` 中配置：
  - 当去除上游数据时，自动清除与上游算子相关的字段

## 5. 字段变更监控
- 在相关字段页面中添加 `checkFieldUpdate` 方法：
  - 定义哪些字段变化会触发下游算子的更新
  - 实现字段变更的联动逻辑