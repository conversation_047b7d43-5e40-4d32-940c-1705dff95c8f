import { createRouter, createWebHistory, RouteRecordRaw } from "vue-router";

const routes: Array<RouteRecordRaw> = [
  {
    path: "/",
    name: "processList",
    component: () => import("@/views/processList/index.vue"),
  },
  {
    path: "/flowChart",
    name: "flowChart",
    component: () => import("@/views/flowChart/index.vue"),
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

export default router;
