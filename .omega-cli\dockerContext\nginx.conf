user nginx;
worker_processes 1;

error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
    '$status $body_bytes_sent "$http_referer" '
    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    sendfile on;
    #tcp_nopush     on;

    keepalive_timeout 65;

    gzip on;
    gzip_comp_level 1;
    gzip_vary on;
    gzip_min_length 20;
    gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript application/x-httpd-php image/jpeg image/gif image/png application/vnd.ms-fontobject font/ttf font/opentype font/x-woff image/svg+xml;
    gzip_buffers 32 4k;

    server {
        listen 80;
        server_name localhost;

        charset utf-8;
        charset_types text/xml text/plain text/vnd.wap.wml application/javascript application/rss+xml text/css;
        ssl_ciphers HIGH:!aNULL:!MD5:!3DES;


        # LowCode location
        location / {
            root   /usr/share/nginx/html/lowcode;
            index  index.html index.htm index.php;
            try_files $uri $uri/ @router;
            client_max_body_size 20m;
        }
        location @router {
            rewrite ^.*$ /index.html last;
        }
        # 解决频繁更新版本导致缓存问题
        location ~* \.html$ {
            # 需要指明具体路径，防止找错文件
            root /usr/share/nginx/html/lowcode;
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires "0";
        }

        location /flink-job {
            proxy_pass http://flinkEngine:18084/flink-job;
            client_max_body_size 1000m;
            proxy_set_header Host $host;
            proxy_set_header Authorization $http_authorization;
        }

        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }
}
