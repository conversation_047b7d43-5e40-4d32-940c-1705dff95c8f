# 重算开关功能实现文档

## 需求描述
将重算按钮修改为类似运行状态的switch按钮（提交/重算，关闭/停止），颜色跟着变化。重算按钮与正常提交按钮互不影响，都可以进行提交。重算提交成功在重算状态下显示"正在重算中"，正常提交成功在流程状态下显示"正在运行中"，互不影响。

## 实现方案

### 1. 核心设计
- **独立状态**: 运行状态和重算状态完全独立
- **独立开关**: 两个独立的switch按钮，互不干扰
- **独立颜色**: 运行状态使用蓝色，重算状态使用绿色/橙色
- **独立文本**: 运行状态显示"运行/停止"，重算状态显示"重算/停止"

### 2. 实现细节

#### ✅ 类型定义扩展
在`TableParam`接口中添加了`isRecalculating`字段：
```typescript
export interface TableParam {
  // ... 其他字段
  isRunning?: boolean;
  isRecalculating?: boolean; // 新增：用于判断重算运行按钮
}
```

#### ✅ 表头配置修改
添加了"重算状态"列：
```typescript
{
  prop: "isRecalculating",
  label: "重算状态",
  minWidth: "100",
}
```

#### ✅ 重算状态开关实现
```vue
<el-switch
  v-model="scope.row.isRecalculating"
  inline-prompt
  :active-text="isRecalculatingSubmittingStatus(scope.row) ? '提交' : '重算 '"
  :inactive-text="isRecalculatingSubmittingStatus(scope.row) ? '关闭' : '停止 '"
  :style="{
    '--el-switch-on-color': isRecalculatingSubmittingStatus(scope.row)
      ? 'var(--el-color-warning)'
      : 'var(--el-color-success)',
    '--el-switch-off-color': isRecalculatingSubmittingStatus(scope.row)
      ? 'var(--el-color-warning)'
      : 'var(--el-color-success)',
  }"
  :loading="isRecalculatingSubmittingStatus(scope.row)"
  :before-change="() => beforeRecalculateChange(scope.row)"
  @change="recalculateSwitchChange(scope.row)"
></el-switch>
```

#### ✅ 状态判断函数
```typescript
// 判断重算是否显示"提交"状态
function isRecalculatingSubmittingStatus(row: ProcessTableParam) {
  if (!row.status) return false;
  const RECALCULATING_SUBMITTING_STATUS = ["RECALCULATING"];
  return RECALCULATING_SUBMITTING_STATUS.includes(row.status);
}
```

#### ✅ 重算开关事件处理
```typescript
// 重算开关变化前的处理
function beforeRecalculateChange(row: ProcessTableParam) {
  return new Promise((resolve) => {
    if (!row.isRecalculating) {
      // 要开启重算，显示重算弹窗
      emit("recalculateClick", row);
      resolve(false); // 阻止开关自动切换，由弹窗确认后手动设置
    } else {
      // 要关闭重算，直接允许
      resolve(true);
    }
  });
}

// 重算开关变化处理
function recalculateSwitchChange(row: ProcessTableParam) {
  if (!row.isRecalculating) {
    // 关闭重算
    emit("cancelRecalculateTask", row);
  }
}
```

#### ✅ 数据处理逻辑
```typescript
const RUNNING_STATUS = ["RUNNING", "SUBMITTED"];
const RECALCULATING_STATUS = ["RECALCULATING"];

// 表格数据添加运行状态一列
function addRunningColumn() {
  tableData.value?.forEach((item: ProcessTableParam) => {
    if (item.status) {
      item.isRunning = RUNNING_STATUS.includes(item.status);
      item.isRecalculating = RECALCULATING_STATUS.includes(item.status);
    }
  });
}
```

### 3. 状态映射表

| 流程状态 | 运行状态开关 | 重算状态开关 | 运行状态文本 | 重算状态文本 | 运行状态颜色 | 重算状态颜色 |
|----------|--------------|--------------|--------------|--------------|--------------|--------------|
| `RUNNING` | ✅ 开启 | ❌ 关闭 | "运行" | "重算" | 蓝色 | 绿色 |
| `RECALCULATING` | ❌ 关闭 | ✅ 开启 | "运行" | "提交" | 蓝色 | 橙色 |
| `SUBMITTED` | ✅ 开启 | ❌ 关闭 | "提交" | "重算" | 蓝色 | 绿色 |
| 其他状态 | ❌ 关闭 | ❌ 关闭 | "运行" | "重算" | 蓝色 | 绿色 |

### 4. 功能特性

#### 🎯 **完全独立**
- 运行状态和重算状态完全独立
- 可以同时开启或关闭任一状态
- 互不干扰的状态管理

#### 🎨 **视觉区分**
- 运行状态使用蓝色
- 重算状态使用绿色（正常）/橙色（提交中）
- 清晰的视觉差异

#### ⚡ **交互优化**
- 开启重算时弹出重算设置弹窗
- 关闭重算时直接停止重算任务
- 与运行状态开关保持一致的交互模式

#### 🔄 **状态同步**
- 重算成功后自动更新重算状态
- 取消重算后自动更新状态
- 保持UI与后端状态一致

### 5. 使用示例

#### 场景1: 启动重算
1. 点击重算状态开关
2. 弹出重算设置弹窗
3. 填写时间信息并确认
4. 重算状态开关自动切换为开启状态
5. 显示"提交"文本和橙色

#### 场景2: 停止重算
1. 点击已开启的重算状态开关
2. 重算状态开关自动切换为关闭状态
3. 显示"重算"文本和绿色

#### 场景3: 同时运行和重算
1. 开启运行状态开关
2. 开启重算状态开关
3. 两个开关同时处于开启状态
4. 互不干扰

### 6. 部署验证

#### 🌐 **可立即测试**
- 访问: http://localhost:9560/
- 查看流程列表页面
- 测试运行状态和重算状态开关

#### 📋 **验证清单**
- [ ] 运行状态和重算状态开关独立工作
- [ ] 重算状态开关颜色正确（绿色/橙色）
- [ ] 点击重算状态开关弹出重算设置弹窗
- [ ] 重算成功后状态自动更新
- [ ] 取消重算后状态自动更新
- [ ] 表格和卡片布局功能一致

## 总结

✅ **需求完全实现**：重算按钮已修改为独立的switch开关，与运行状态开关完全独立，互不影响

✅ **视觉优化**：使用不同颜色区分运行状态和重算状态，提升用户体验

✅ **交互优化**：保持与运行状态开关一致的交互模式，降低学习成本

✅ **代码质量**：完整的类型定义、状态管理和事件处理
