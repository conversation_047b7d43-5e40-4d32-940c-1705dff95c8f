<template>
  <el-drawer v-model="showDrawer" :with-header="false" size="60%">
    <div class="header">
      <span>当前日志任务ID:</span>
      <el-input
        v-model="value"
        placeholder="请输入"
        style="width: 200px"
      ></el-input>
    </div>
    <div class="content" id="logContainer"></div>
    <!-- <div class="footer">
      <el-button @click="cancelClick">关闭</el-button>
      <el-button @click="confirmClick">保存</el-button>
    </div> -->
  </el-drawer>
</template>

<script setup lang="ts">
// 弹窗的打开和关闭,获取父组件的v-model的值
const showDrawer = defineModel<boolean>({ default: false });

// 数据监听
watch(
  () => showDrawer.value,
  (val) => {
    if (val) {
      nextTick(() => {
        replaceText();
      });
    }
  }
);

const value = ref();

// 进行文本的切割展示
function replaceText() {
  const logText = `当前日志文件:E:\\ETLCloud-share\\windows-tomcat-V3.1\\tomcat\\webapps\\ROOT\\log\\etl01\\20240905\\66d997ef932f285b78ea3785.log ETL01:test at 09/05 19:37:19 traceId:66d997ef932f285b78ea3785=>调试信息=>***********启动一个新流程(66d997ef932f285b78ea3785),所属应用:12007811,名称:test1******************** ETL01:test at 09/05 19:37:19traceId:66d997ef932f285b78ea3785=>调试信息=>提示:流程初始化成功,事务id为:66d997ef932f285b78ea3785ETL01:test at 09/05 19:37:19 traceId:66d997ef932f285b78ea3785=>test1>执行结果>null 共耗时:2毫秒 是否成功=是`;
  // 使用正则表达式匹配整个日志条目，包括起始部分
  const regexString = /(ETL01:[^\r\n]*?(?=ETL01:|$))/g;
  const logPattern = new RegExp(regexString, "g");

  // 将日志文本分割成数组
  const logEntries = logText.match(logPattern);
  // 获取日志容器
  const logContainer = document.getElementById("logContainer");
  // 遍历日志条目并创建段落元素展示
  if (logEntries) {
    logEntries.forEach((entry) => {
      const logEntryDiv = document.createElement("div");
      logEntryDiv.className = "log-entry";
      // 替换换行符为HTML的换行标签
      logEntryDiv.innerHTML = entry.replace(/\n/g, "<br>").replace(/\r/g, "");
      logContainer?.appendChild(logEntryDiv);
    });
  } else {
    console.log("No log entries found.");
  }
}

function cancelClick() {
  showDrawer.value = false;
}
</script>

<style lang="scss" scoped>
.header {
  display: flex;
  align-items: center;
  span {
    margin-right: 8px;
  }
}
.content {
  margin-top: 20px;
  height: calc(100% - 100px);
  overflow-y: auto;
}
.footer {
  text-align: center;
  margin-top: 20px;
}
</style>
