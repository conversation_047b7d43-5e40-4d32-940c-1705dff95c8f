import fetch from "@/utils/fetch";
import {
  QueryArgusParam,
  SaveFlinkJobParam,
  UpdateLayoutParam,
  QueryLayoutResponse,
  QueryArgusResponse,
  QueryFieldParam,
  QueryJoinParam,
  QueryGroupParam,
  QueryDebugParam,
  QueryDebugResponse,
  QueryExceptionParam,
  abnormalOptionParam,
} from "./types";

// 查询算子具体配置参数信息
export function queryArgus(
  data: QueryArgusParam
): PromisedCommonResp<QueryArgusResponse[]> {
  return fetch({
    url: `/flink-job/queryArgus?id=${data.id}`,
    method: "GET",
  });
}

// 查询流程图布局位置信息
export function queryLayout(
  data: QueryArgusParam
): PromisedCommonResp<QueryLayoutResponse[]> {
  return fetch({
    url: `/flink-job/queryLayout?id=${data.id}`,
    method: "GET",
  });
}

// 保存算子具体配置参数信息
export function saveFlinkJob(
  data: SaveFlinkJobParam
): PromisedCommonResp<ProcessTableParam[]> {
  return fetch({
    url: "/flink-job/save",
    method: "PUT",
    data,
    timeout: 120000,
  });
}

// 保存流程图布局位置信息
export function updateLayout(
  data: UpdateLayoutParam
): PromisedCommonResp<ProcessTableParam[]> {
  return fetch({
    url: "/flink-job/updateLayout",
    method: "PUT",
    data,
    timeout: 120000,
  });
}

// 定时记录算子--查询算子的字段类型接口
export function queryFieldType(
  data: QueryFieldParam
): PromisedCommonResp<FieldTableParam[]> {
  return fetch({
    url: `/flink-job/queryFields?functionType=${data.functionType}`,
    method: "GET",
  });
}

// 业务维度配置算子 -- 查询算子中的输入源数据
export function queryModelsData(): PromisedCommonResp<ModelsParam[]> {
  return fetch({
    url: "/flink-job/queryModels",
    method: "GET",
  });
}

// join连接算子 -- 获取算子中合并后的字段数据
export function queryJoinModelsData(
  data: QueryJoinParam
): PromisedCommonResp<FieldTableParam[]> {
  return fetch({
    url: "/flink-job/queryJoinedFields",
    method: "POST",
    data,
  });
}

// 分组汇总算子 -- 获取算子中分组后的字段数据
export function queryGroupModelsData(
  data: QueryGroupParam
): PromisedCommonResp<FieldTableParam[]> {
  return fetch({
    url: "/flink-job/queryGroupedFields",
    method: "POST",
    data,
  });
}

// 筛选算子 -- 枚举字段的枚举值获取
export function queryEnumerationsData(
  data: QueryArgusParam
): PromisedCommonResp<OptionsParam[]> {
  return fetch({
    url: `/flink-job/queryEnumerations?enumClassName=${data.id}`,
    method: "GET",
  });
}

// 模型输出算子 -- 获取所有的物理模型
export function getAllPhysicalModels(): PromisedCommonResp<ModelParam[]> {
  return fetch({
    url: `/flink-job/getAllPhysicalModels`,
    method: "GET",
  });
}

// 模型输出算子 -- 获取所有的物理模型中的所有字段
export function getPhysicalModelDetails(data: {
  modelLabel: number | string;
}): PromisedCommonResp<ModelFieldParam> {
  return fetch({
    url: `/flink-job/getPhysicalModelDetails?modelLabel=${data.modelLabel}`,
    method: "GET",
  });
}

// 查询调试后的文件数据信息
export function getDebugFile(
  data: QueryDebugParam
): Promise<QueryDebugResponse> {
  return fetch({
    url: `/flink-job/getFile`,
    method: "POST",
    data,
  });
}

// 查询模型算子关联字段的类型校验是否正确
export function checkModelAssociation(data: {
  modelAssociation?: ModelAssociationTableParam[];
}): PromisedCommonResp<boolean> {
  return fetch({
    url: `/flink-job/checkModelAssociation`,
    method: "POST",
    data,
  });
}

// 字段设置算子  -- 检验新增字段算子中的公式是否正确
export function checkFieldFormula(data: {
  tableArray?: FieldTableParam[];
}): PromisedCommonResp<boolean> {
  return fetch({
    url: `/flink-job/validateFieldFormula`,
    method: "POST",
    data,
  });
}

// redis定制业务模型算子-- 获取输入物理模型中的所有字段
export function getRedisMeta(data: {
  modelLabel: number | string;
}): PromisedCommonResp<RedisModelParam[]> {
  return fetch({
    url: `/flink-job/getRedisMeta?modelLabel=${data.modelLabel}`,
    method: "GET",
  });
}

/**
 * @description: 异常处理算子  -- 获取异常处理算子中的业务场景
 * @return {*}
 */
export function getBusinessScenarios(): PromisedCommonResp<OptionsParam[]> {
  return fetch({
    url: `/flink-job/getBusinessScenarios`,
    method: "GET",
  });
}

/**
 * @description: 异常处理算子-- 获取业务场景的所有必填字段
 * @param {object} data
 * @return {*}
 */
export function getRequiredParameters(data: {
  id: number | string;
}): PromisedCommonResp<DynamicOptionsParam[]> {
  return fetch({
    url: `/flink-job/getRequiredParameters?id=${data.id}`,
    method: "GET",
  });
}

/**
 * @description: 异常处理算子-- 获取业务场景的所有非必填字段
 * @param {object} data
 * @return {*}
 */
export function getOptionalParameters(data: {
  id: number | string;
}): PromisedCommonResp<abnormalOptionParam[]> {
  return fetch({
    url: `/flink-job/getOptionalParameters?id=${data.id}`,
    method: "GET",
  });
}

/**
 * @description: 异常处理算子-- 获取处理后的字段返回
 * @param {QueryExceptionParam} data
 * @return {*}
 */
export function queryProcessedFields(
  data: QueryExceptionParam
): PromisedCommonResp<FieldTableParam[]> {
  return fetch({
    url: `/flink-job/queryProcessedFields`,
    method: "POST",
    data,
  });
}

/**
 * @description: 模型数据输入算子 -- 通过模型名称获取模型字段
 * @param {string} modelLabel
 * @return {*}
 */
export function queryModelFields(data: {
  modelLabel: string;
}): PromisedCommonResp<FieldTableParam[]> {
  return fetch({
    url: `/flink-job/source/queryModelFields?modelLabel=${data.modelLabel}`,
    method: "GET",
  });
}
