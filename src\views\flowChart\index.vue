<template>
  <div class="dnd-flow" @drop="onDrop">
    <!-- 顶部工具栏 -->
    <HeaderControls
      :id="flowId"
      :jobName="flowJobName"
      :alias="alias"
      :status="flowStatus"
      :isTableLayout="isTableLayout"
      @clearClick="clearFlow"
    ></HeaderControls>
    <div class="flow-container">
      <!-- 左侧算子列表 -->
      <Sidebar :id="flowId" v-show="isShowSidebar" />
      <!-- 右侧流程图 -->
      <VueFlow
        :nodes="nodes"
        :edges="edges"
        @dragover="onDragOver"
        @dragleave="onDragLeave"
      >
        <!-- 流程图背景 -->
        <DropzoneBackground
          :style="{
            backgroundColor: isDragOver ? '#e7f3ff' : '#F1F3FA',
            transition: 'background-color 0.2s ease',
          }"
        >
          <p v-if="isDragOver">Drop here</p>
        </DropzoneBackground>
        <!-- 控制台展示 -->
        <CustomControls />
        <!-- 小地图展示 -->
        <CustomMiniMap />
        <!-- 默认节点 -->
        <template #node-default="props">
          <CustomNode
            :id="props.id"
            :data="props.data"
            @editNodeInfo="editDrawer"
          />
        </template>
        <template #edge-step="edgeProps">
          <CustomEdge v-bind="edgeProps"></CustomEdge>
        </template>
        <template #edge-straight="edgeProps">
          <CustomEdge v-bind="edgeProps"></CustomEdge>
        </template>
        <template #edge-default="edgeProps">
          <CustomEdge v-bind="edgeProps"></CustomEdge>
        </template>
      </VueFlow>
    </div>
    <!-- 编辑侧边栏 -->
    <EditDrawerInfo
      v-model="showDrawer"
      :inputData="inputData"
      :showConfirm="isShowSidebar"
      @updateNodeInfo="updateNodeInfo"
    ></EditDrawerInfo>
  </div>
</template>

<script setup lang="ts">
import { VueFlow, useVueFlow } from "@vue-flow/core";

const {
  onConnect,
  updateNode,
  onEdgesChange,
  onNodesChange,
  onNodeClick,
  onEdgeClick,
  toObject,
} = useVueFlow();

// 引入控制台
import CustomControls from "./components/CustomControls.vue";

// 引入小地图
import CustomMiniMap from "./components/CustomMiniMap.vue";

// 引入拖拽交互逻辑
import useDragAndDrop from "@/utils/useDrag";
const { onDragOver, onDrop, onDragLeave, isDragOver, getCurrentUid } =
  useDragAndDrop();

// 引入左侧工具栏
import Sidebar from "./components/Sidebar.vue";

//  引入流程图背景
import DropzoneBackground from "./components/DropzoneBackground.vue";

// 引入编辑侧边栏
import EditDrawerInfo from "./components/CustomDrawer/editDrawer.vue";

// 引入顶部控制台
import HeaderControls from "./components/headerControls.vue";

//  引入自定义节点
import CustomNode from "./components/CustomNode.vue";

// 引入自定义连接线
import CustomEdge from "./components/CustomEdge.vue";

const flowId = ref<string>("");

const flowJobName = ref<string>("");

const alias = ref<string>("");
// 当前流程图的状态
const flowStatus = ref<string>("");

const isTableLayout = ref<boolean>(false);

// 获取路由参数
const routeState = window.history.state;

watch(
  () => routeState.id,
  (newVal) => {
    if (newVal) {
      nextTick(() => {
        flowId.value = String(routeState.id);
        flowJobName.value = routeState.jobName as string;
        alias.value = routeState.alias as string;
        flowStatus.value = routeState.status as string;
        isTableLayout.value = routeState.isTableLayout == "true";
        flowChartStore.updateResource({ jobName: flowJobName.value });
        getFlowData(flowId.value);
      });
    }
  },
  { immediate: true, deep: true }
);

// 左侧算子列表是否展示
const isShowSidebar = computed(() => {
  return flowStatus.value === "design";
});

// 获取流程图完整图形展示
function getFlowData(id: string | number) {
  getFlowDataById(id);
  getArgus(id);
}

// 接口导入
import customApi from "@/api/custom";

// 获取流程图的位置信息
async function getFlowDataById(id: string | number) {
  const res = await customApi.queryLayout({ id });
  if (res.code === 0) {
    const data = res.data[0] || {};
    const layout = data.layout ? JSON.parse(data.layout) : [];
    if (isEmpty(layout)) return;
    nodes.value = layout[0].nodes;
    edges.value = layout[0].edges;
    // 获取当前最新的Uid信息
    getCurrentUid(nodes.value, id);
  }
}

import { useflowChartStore } from "@/store/modules/flowChart";
const flowChartStore = useflowChartStore();

async function getArgus(id: string | number) {
  const res = await customApi.queryArgus({ id });
  if (res.code === 0) {
    const data = res.data[0] || {};
    const argues = data.argues ? JSON.parse(data.argues) : [];
    if (isEmpty(argues)) return;
    flowChartStore.getInitArguesData(argues);
  }
}

import type { Node, Edge } from "@vue-flow/core";

const nodes = ref<Node[]>([]);

const edges = ref<Edge[]>([]);

// 添加自定义连接线
onConnect((params) => {
  edges.value.push({
    id: params.source + "-" + params.target,
    source: params.source,
    sourceHandle: params.sourceHandle,
    target: params.target,
    targetHandle: params.targetHandle,
    type: flowChartStore.edgeType,
  });
});

const clickOneEdge = ref<Edge>();

// 删除其需要先进行选中处理
onEdgeClick((change) => {
  clickOneEdge.value = change.edge;
  // 临时保存所有的节点信息
  allNodeData.value = toObject();
});

// 监听删除连接线操作
onEdgesChange((changes) => {
  for (const change of changes) {
    if (change.type !== "remove") continue;
    edges.value = edges.value.filter((edge) => edge.id !== change.id);
    if (change.id !== clickOneEdge.value?.id) continue;
    // 清空数据
    clearChildrenNode(
      change.target,
      allNodeData.value.nodes,
      allNodeData.value.edges,
      "deleteEdge"
    );
  }
});

const clickOneNode = ref<Node>();

const allNodeData = ref();

// 删除其需要先进行选中处理
onNodeClick((change) => {
  clickOneNode.value = change.node;
  // 临时保存所有的节点信息
  allNodeData.value = toObject();
});

import { clearChildrenNode } from "@/utils/common";

// 监听删除节点操作
onNodesChange((changes) => {
  for (const change of changes) {
    if (change.type !== "remove") continue;
    if (change.id !== clickOneNode.value?.id) continue;
    // 清空数据
    clearChildrenNode(
      change.id,
      allNodeData.value.nodes,
      allNodeData.value.edges,
      "deleteNode"
    );
  }
});

// 点击编辑
const showDrawer = ref(false);

const inputData = ref<EditDrawerParam>({
  id: "",
  name: "",
});

// 传递给孙子组件使用
provide("inputData", inputData);

// 编辑节点
function editDrawer(data: EditDrawerParam) {
  let filterData;
  switch (data.nodeType) {
    case "source":
      filterData = flowChartStore.inputOperatorData.filter(
        (item) => item.id === data.id
      );
      break;
    case "sink":
      filterData = flowChartStore.outputOperatorData.filter(
        (item) => item.id === data.id
      );
      break;
    default:
      filterData = flowChartStore.computerOperatorData.filter(
        (item) => item.id === data.id
      );
  }
  inputData.value = filterData.length > 0 ? filterData[0] : data;
  showDrawer.value = true;
}

// 更新节点数据
function updateNodeInfo(dataInfo: EditDrawerParam) {
  const newData = {
    name: dataInfo.name,
    nodeType: dataInfo.nodeType,
    type: dataInfo.type,
    dataStream: dataInfo.dataStream,
  };
  // 更新节点
  updateNode(dataInfo.id, { data: newData });
}

// 清空流程列表数据
function clearFlow() {
  flowChartStore.clearOperatorData();
  nodes.value.length = 0;
  edges.value.length = 0;
}
</script>

<style lang="scss" scoped>
.dnd-flow {
  flex-direction: column;
  display: flex;
  height: 100%;
  // 禁止页面复制选择
  user-select: none;
  .flow-container {
    flex: 1;
    display: flex;
    flex-direction: row;
    :deep(.vue-flow__node) {
      padding: 0;
      box-sizing: border-box;
    }
    :deep(.vue-flow__node.selected) {
      border: 2px solid #165dff;
      box-shadow: none;
    }
  }
}
</style>
